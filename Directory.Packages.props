<!-- For more info on central package management go to https://devblogs.microsoft.com/nuget/introducing-central-package-management/ -->
<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="Ardalis.GuardClauses" Version="4.6.0" />
    <PackageVersion Include="AutoMapper" Version="13.0.1" />
    <PackageVersion Include="AWSSDK.CognitoIdentityProvider" Version="3.7.403.35" />
    <PackageVersion Include="AWSSDK.Core" Version="3.7.400.54" />
    <PackageVersion Include="AWSSDK.DynamoDBv2" Version="3.7.404" />
    <PackageVersion Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.301" />
    <PackageVersion Include="AWSSDK.SecurityToken" Version="3.7.401.7" />
    <PackageVersion Include="AWSSDK.SSO" Version="3.7.400.55" />
    <PackageVersion Include="AWSSDK.SSOOIDC" Version="3.7.400.55" />
    <PackageVersion Include="Carter" Version="8.2.1" />
    <PackageVersion Include="coverlet.collector" Version="6.0.2" />
    <PackageVersion Include="FluentAssertions" Version="6.12.0" />
    <PackageVersion Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.2" />
    <PackageVersion Include="MediatR" Version="12.4.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.16" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.8" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="8.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Caching.Memory" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
    <PackageVersion Include="Microsoft.IdentityModel.Tokens" Version="8.4.0" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.11.0" />
    <PackageVersion Include="Moq" Version="4.20.70" />
    <PackageVersion Include="N5.Platform.Instrumentation" Version="0.9.2" />
    <PackageVersion Include="N5.Result" Version="1.0.0" />
    <PackageVersion Include="NSwag.AspNetCore" Version="14.1.0" />
    <PackageVersion Include="NSwag.MSBuild" Version="14.1.0" />
    <PackageVersion Include="nunit" Version="3.14.0" />
    <PackageVersion Include="NUnit.Analyzers" Version="3.9.0" />
    <PackageVersion Include="NUnit3TestAdapter" Version="4.5.0" />
    <PackageVersion Include="Refit" Version="8.0.0" />
    <PackageVersion Include="Refit.HttpClientFactory" Version="8.0.0" />
    <PackageVersion Include="Respawn" Version="6.2.1" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="7.0.0" />
    <PackageVersion Include="System.IdentityModel.Tokens.Jwt" Version="8.4.0" />
  </ItemGroup>
</Project>