﻿using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Application.Common.Models;

namespace UsersManagement.Application.Management.Queries.GetLicenses;

public record GetLicenseQuery() : IRequest<Result<GetLicenseResponse>>;

internal class GetLicenseQueryHandler(ILicenseService licenseService, ITenantProvider tenantProvider) : IRequestHandler<GetLicenseQuery, Result<GetLicenseResponse>>
{
    public async Task<Result<GetLicenseResponse>> Handle(GetLicenseQuery request, CancellationToken cancellationToken)
    {
        var license = await licenseService.GetLicenseAsync(tenantProvider.GetTenantId(), cancellationToken);

        if (license.IsFailure)
            return Result.Failure<GetLicenseResponse>(license.Error);

        return Result.Success(GetLicenseResponse.FromLicense(license.Value));
    }
}
