﻿using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Application.Common.Models;

namespace UsersManagement.Application.Management.Queries.GetUsers;

public record GetUsersCommand : IRequest<Result<GetUsersResponse>>
{
    public string? Cursor { get; init; }
    public int Limit { get; init; }
}

internal class GetUsersCommandHandler(IIdentityService identityService) : IRequestHandler<GetUsersCommand, Result<GetUsersResponse>>
{
    public async Task<Result<GetUsersResponse>> <PERSON>le(GetUsersCommand request, CancellationToken cancellationToken)
    {
        var users = await identityService.GetUsersAsync(request.Cursor, request.Limit, cancellationToken);

        if (users.IsFailure)
            return Result.Failure<GetUsersResponse>(users.Error);

        return Result.Success(new GetUsersResponse(users.Value.users.Select(UserDTO.FromUser), users.Value.paginationToken));
    }
}
