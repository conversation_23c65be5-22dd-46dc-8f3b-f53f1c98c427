﻿using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Domain.Entities;

namespace UsersManagement.Application.Management.Queries.GetPermissions;

public record GetPermissionsQuery() : IRequest<Result<List<Permission>>>
{

}

public class GetPermissionsQueryHandler(IPermissionsService permissionsService, ITenantProvider tenantProvider) : IRequestHandler<GetPermissionsQuery, Result<List<Permission>>>
{
    public async Task<Result<List<Permission>>> Handle(GetPermissionsQuery request, CancellationToken cancellationToken)
    {
        return await permissionsService.GetAllPermissions(tenantProvider.GetTenantId(), cancellationToken);
    }
}
