﻿namespace UsersManagement.Application.Management.Commands.EditUser;

public class EditUserCommandValidator : AbstractValidator<EditUserCommand>
{
    public EditUserCommandValidator()
    {
        RuleFor(v => v.Email)
            .NotEmpty()
            .EmailAddress();

        RuleFor(v => v.Request)
            .ChildRules(request =>
            {
                //Al menos una propiedad con valor
                request
                    .RuleFor(v => v)
                    .Must(vReq => !string.IsNullOrWhiteSpace(vReq.NewEmail) || vReq.Role != null || (vReq.Permissions?.Count ?? 0) != 0)
                    .WithMessage("At least one property must be updated.");

                request.RuleFor(v => v.NewEmail)
                    .NotEmpty()
                    .EmailAddress();
            });
    }
}
