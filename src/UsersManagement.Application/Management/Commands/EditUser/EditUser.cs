﻿using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Domain.DTO;

namespace UsersManagement.Application.Management.Commands.EditUser;

public record EditUserCommand : IRequest<Result>
{
    public string Email { get; init; } = string.Empty;

    public UpdateUserRequest Request { get; init; } = new();
}

internal class EditUserCommandHandler(IIdentityService identityService, IPermissionsService permissionsService, ITenantProvider tenantProvider) : IRequestHandler<EditUserCommand, Result>
{
    public async Task<Result> Handle(EditUserCommand request, CancellationToken cancellationToken)
    {
        var tenantId = tenantProvider.GetTenantId();
        var userResponse = await identityService.GetUserAsync(request.Email, cancellationToken);
        
        if (userResponse.IsFailure)
            return Result.Failure(userResponse.Error);

        var needUpdatePermissions = (request.Request.Permissions?.Count ?? 0) != 0;

        if (needUpdatePermissions)
        {
            var existsPermissionsResult = await permissionsService.ExistsPermissions(tenantId, request.Request.Permissions!, cancellationToken);

            if (existsPermissionsResult.IsFailure)
                return Result.Failure(existsPermissionsResult.Error);
            
            if (!existsPermissionsResult.Value)
                return Result.Failure(Domain.Errors.CommandsErrors.PermissionsError.PERMISSIONS_ERROR);
        }

        var userEditionResponse = await identityService.EditUserAsync(request.Email, request.Request, tenantId, cancellationToken);
        
        if (userEditionResponse.IsFailure)
            return Result.Failure(userEditionResponse.Error);

        if (needUpdatePermissions)
        {
            var permissionsToDelete = userResponse.Value.Permissions.Except(request.Request.Permissions!).ToList();
            var permissionsToAdd = request.Request.Permissions!.Except(userResponse.Value.Permissions).ToList();
            if(permissionsToDelete.Count > 0)
                await permissionsService.AddPermissionUserCount(tenantId, permissionsToDelete, -1, cancellationToken);
            if (permissionsToAdd.Count > 0)
                await permissionsService.AddPermissionUserCount(tenantId, permissionsToAdd, 1, cancellationToken);
        }
        
        return Result.Success();
    }
}
