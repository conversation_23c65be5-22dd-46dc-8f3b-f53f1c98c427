﻿using UsersManagement.Application.Common.Interfaces;

namespace UsersManagement.Application.Management.Commands.DeleteUser;

public record DeleteUserCommand : IRequest<Result>
{
    public string Email { get; init; } = string.Empty;
    public string Permissions { get; init; } = string.Empty;
}

public class DeleteUserCommandHandler(ITenantProvider tenantProvider, IIdentityService identityService, ILicenseService licenseService, IPermissionsService permissionsService) : IRequestHandler<DeleteUserCommand, Result>
{
    public async Task<Result> Handle(DeleteUserCommand request, CancellationToken cancellationToken)
    {
        var tenantId = tenantProvider.GetTenantId();
        var user = await identityService.GetUserAsync(request.Email, cancellationToken);
        
        if (user.IsFailure)
            return Result.Failure(new Error("USER_NOT_FOUND", "User not found."));

        var refundResult = await licenseService.Refund(tenantId, cancellationToken);

        if(refundResult.IsFailure)
            return Result.Failure(refundResult.Error);

        var deleteUserResult = await identityService.DeleteUserAsync(request.Email, cancellationToken);

        if (deleteUserResult.IsFailure)
        {
            await licenseService.Consume(tenantId, cancellationToken);
        
            return Result.Failure(deleteUserResult.Error);
        }

        var permissionsCountResult = permissionsService.AddPermissionUserCount(tenantId, user.Value.Permissions, -1, cancellationToken);

        return deleteUserResult;
    }
}
