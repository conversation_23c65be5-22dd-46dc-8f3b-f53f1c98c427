﻿namespace UsersManagement.Application.Management.Commands.RegisterUsers;
public class DeleteUserCommandValidator : AbstractValidator<RegisterUsersCommand>
{
    public DeleteUserCommandValidator()
    {
        RuleFor(v => v.Users)
            .NotEmpty();

        RuleForEach(v => v.Users).ChildRules(user =>
        {
            user.RuleFor(v => v.Email)
                .EmailAddress()
                .NotEmpty();

            user.RuleFor(v => v.Role)
                .NotEmpty();
        });
            
    }
}
