﻿using Microsoft.Extensions.Logging;
using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Application.Common.Models;
using UsersManagement.Domain.Enums;
using UsersManagement.Domain.Errors;

namespace UsersManagement.Application.Management.Commands.RegisterUsers;
public record RegisterUsersCommand : IRequest<Result>
{
    public IList<UserRegistration> Users { get; init; } = new List<UserRegistration>();
}
public class RegisterUsersCommandHandler(IIdentityService identityService, ILicenseService licenseService, IPermissionsService permissionsService, ITenantProvider tenantProvider, ILogger<RegisterUsersCommandHandler> logger) : IRequestHandler<RegisterUsersCommand, Result>
{
    public async Task<Result> Handle(RegisterUsersCommand request, CancellationToken cancellationToken)
    {
        var tenantId = tenantProvider.GetTenantId();
        
        var allPermissions = request.Users.SelectMany(x => x.Permissions).Distinct().ToList();

        var existsPermissions = await permissionsService.ExistsPermissions(tenantId, request.Users.SelectMany(x => x.Permissions).Distinct().ToList(), cancellationToken);

        if (existsPermissions.IsFailure)
            return Result.Failure(existsPermissions.Error);

        if (!existsPermissions.Value)
            return Result.Failure(CommandsErrors.PermissionsError.PERMISSIONS_ERROR);

        var consumeLicensesCount = request.Users.Count;
        int i = 0;

        try
        {
            for (i = 0; i < consumeLicensesCount; i++)
            {
                var consumeResult = await licenseService.Consume(tenantId, cancellationToken);

                if (consumeResult.IsFailure)
                    throw new Exception($"{consumeResult.Error.Code}|{consumeResult.Error.Message}");
            }

            var result = await identityService.CreateUsersAsync(tenantId, request.Users
                .Select(x => Domain.Entities.User.Create(null, null, x.Email, x.Role, UserOrigin.Standalone, x.Permissions))
                .ToList(), cancellationToken: cancellationToken);

            if (result.IsFailure)
            {
                for (; i > 0; i--)
                    await licenseService.Refund(tenantId, cancellationToken);

                return Result.Failure(result.Error);
            }

            await UpdatePermissions(tenantId, allPermissions, consumeLicensesCount, cancellationToken);

            //Se crearon todos los usuarios
            if (result.Value.createdQty == consumeLicensesCount)
                return Result.Success();

            i = consumeLicensesCount - result.Value.createdQty;
            for (; i > 0; i--)
                await licenseService.Refund(tenantId, cancellationToken);

            if (result.Value.createdQty == 0)
                return Result.Failure(new Error("USERS_CREATION_FAILED", "User creation failed. No user was created."));
            
            return Result.Failure(new Error("SOME_USERS_CREATION_FAILED", "User creation failed. Some users were created"));
        }
        catch (Exception e)
        {
            for (; i > 0; i--)
                await licenseService.Refund(tenantId, cancellationToken);

            var splittedMessage = e.Message.Split('|');

            if(splittedMessage.Length == 2)
                return Result.Failure(new Error(splittedMessage[0], splittedMessage[1]));

            return Result.Failure(new Error("SOME_USERS_CREATION_FAILED", "User creation failed. Some users were created"));
        }
    }

    private async Task UpdatePermissions(string tenantId, List<string> permissions, int userCount, CancellationToken cancellationToken)
    {
        if (userCount == 0)
            return;

        var result = await permissionsService.AddPermissionUserCount(tenantId, permissions, userCount, cancellationToken);

        if (result.IsFailure)
            logger.LogError("Update permissions failed");
    }
}
