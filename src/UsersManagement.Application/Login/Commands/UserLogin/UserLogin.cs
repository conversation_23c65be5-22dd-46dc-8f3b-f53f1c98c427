﻿using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Application.Common.Models;
using System.Diagnostics;
namespace UsersManagement.Application.Login.Commands.UserLogin;

public record UserLoginCommand : IRequest<Result<LoginResponse>>
{
    public string Email { get; init; } = string.Empty;
    public string Password { get; init; } = string.Empty;
}


internal class UserLoginCommandHandler(IIdentityService identityService, ILicenseService licenseService, ITenantProvider tenantProvider) : IRequestHandler<UserLoginCommand, Result<LoginResponse>>
{
    public async Task<Result<LoginResponse>> Handle(UserLoginCommand request, CancellationToken cancellationToken)
    {
        Activity.Current?.SetTag("platform.auth.user", request.Email);

        var tenantIdResult = await identityService.GetTenantIdByNameAndCountry(tenantProvider.GetTenantName(), tenantProvider.GetTenantCountry(), cancellationToken);

        if (tenantIdResult.IsFailure)
            return Result.Failure<LoginResponse>(tenantIdResult.Error);

        var license = await licenseService.GetLicenseAsync(tenantIdResult.Value, cancellationToken);

        if(license.IsFailure)
            return Result.Failure<LoginResponse>(license.Error);

        if(license.Value.ExpirationDate < DateTime.UtcNow)
            return Result.Failure<LoginResponse>(Domain.Errors.LoginErrors.License.EXPIRED);

        return await identityService.LoginAsync(tenantIdResult.Value, request.Email, request.Password, cancellationToken);
    }
}
