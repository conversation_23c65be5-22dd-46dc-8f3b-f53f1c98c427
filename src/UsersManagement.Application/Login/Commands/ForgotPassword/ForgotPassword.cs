﻿
using UsersManagement.Application.Common.Interfaces;

namespace UsersManagement.Application.Login.Commands.ForgotPassword;

public record ForgotPasswordCommand : IRequest<Result>
{
    public string Email { get; init; } = string.Empty;
}

public class ForgotPasswordCommandHandler(IIdentityService identityService, ITenantProvider tenantProvider) : IRequestHandler<ForgotPasswordCommand, Result>
{
    public async Task<Result> Handle(ForgotPasswordCommand request, CancellationToken cancellationToken)
    {
        var tenantName = tenantProvider.GetTenantName();
        var tenantCountry = tenantProvider.GetTenantCountry();

        var tenantIdResult = await identityService.GetTenantIdByNameAndCountry(tenantName, tenantCountry, cancellationToken);

        if (tenantIdResult.IsFailure)
            return Result.Failure(tenantIdResult.Error);

        return await identityService.ForgotPasswordAsync(tenantIdResult.Value, request.Email, cancellationToken);
    }
}
