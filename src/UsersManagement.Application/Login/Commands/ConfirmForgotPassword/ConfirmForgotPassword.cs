﻿
using UsersManagement.Application.Common.Interfaces;

namespace UsersManagement.Application.Login.Commands.ConfirmForgotPassword;

public record ConfirmForgotPasswordCommand : IRequest<Result>
{
    public string Email { get; init; } = string.Empty;
    public string Code { get; init; } = string.Empty;
    public string Password { get; init; } = string.Empty;
}

public class ConfirmForgotPasswordCommandHandler(IIdentityService identityService, ITenantProvider tenantProvider) : IRequestHandler<ConfirmForgotPasswordCommand, Result>
{
    public async Task<Result> Handle(ConfirmForgotPasswordCommand request, CancellationToken cancellationToken)
    {
        var tenantName = tenantProvider.GetTenantName();
        var tenantCountry = tenantProvider.GetTenantCountry();

        var tenantIdResult = await identityService.GetTenantIdByNameAndCountry(tenantName, tenantCountry, cancellationToken);

        if (tenantIdResult.IsFailure)
            return Result.Failure(tenantIdResult.Error);

        return await identityService.ConfirmForgotPasswordAsync(tenantIdResult.Value, request.Email, request.Password, request.Code, cancellationToken);
    }
}
