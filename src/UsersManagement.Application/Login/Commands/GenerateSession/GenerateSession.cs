﻿using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.Extensions.Logging;
using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Application.Common.Models;
using UsersManagement.Application.Login.Commands.StoreJwt;
using UsersManagement.Domain.Entities;

namespace UsersManagement.Application.Login.Commands.GenerateSession;

public record GenerateSessionCommand : IRequest<Result<LoginResponse>>
{
    public string SessionCode { get; init; } = string.Empty;
}

public class GenerateSessionCommandHandler(
    ISessionService sessionService, 
    ITenantProvider tenantProvider, 
    IIdentityService identityService, 
    ISettingsService settingsService, 
    IExternalJwtValidator externalJwtValidator,
    IPasswordGenerator passwordGenerator,
    ILicenseService licenseService,
    ILogger<GenerateSessionCommandHandler> logger
) : IRequestHandler<GenerateSessionCommand, Result<LoginResponse>>
{
    public async Task<Result<LoginResponse>> Handle(GenerateSessionCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var tenantName = tenantProvider.GetTenantName();
            var tenantCountry = tenantProvider.GetTenantCountry();

            //Obtengo el tenantId
            var tenantIdResult = await identityService.GetTenantIdByNameAndCountry(tenantName, tenantCountry, cancellationToken);

            if (tenantIdResult.IsFailure)
                return Result.Failure<LoginResponse>(tenantIdResult.Error);

            var jwtResultTask = sessionService.GetJwtAsync(tenantIdResult.Value, request.SessionCode, cancellationToken);
            var jwkResultTask = settingsService.GetJWKAsync(tenantIdResult.Value, cancellationToken);

            await Task.WhenAll(jwtResultTask, jwkResultTask);

            if (jwtResultTask.Result.IsFailure)
                return Result.Failure<LoginResponse>(jwtResultTask.Result.Error);

            if (jwkResultTask.Result.IsFailure)
                return Result.Failure<LoginResponse>(jwkResultTask.Result.Error);

            var jwtValidationResult = await externalJwtValidator.ValidateJwtByJwkAsync(jwkResultTask.Result.Value, jwtResultTask.Result.Value);

            if (jwtValidationResult.IsFailure)
                return Result.Failure<LoginResponse>(jwtValidationResult.Error);

            //Obtengo y valido el email de los claims del JWT
            var email = jwtValidationResult.Value.FindFirst(JwtRegisteredClaimNames.Email)?.Value;

            if (string.IsNullOrWhiteSpace(email) || !new FluentValidation.Validators.AspNetCoreCompatibleEmailValidator<string>().IsValid(null, email))
                return Result.Failure<LoginResponse>(new Error("INVALID_TOKEN_CLAIMS", "Invalid token claims"));

            //Valido si el usuario existe en nuestro idp
            var userExist = await identityService.UserExists(tenantIdResult.Value, email, cancellationToken);

            if (userExist.IsFailure)
                return Result.Failure<LoginResponse>(userExist.Error);

            //Genero una contraseña aleatoria
            var password = passwordGenerator.Generate();

            if (!userExist.Value)
            {
                var roles = GetRolesFromClaims(jwtValidationResult.Value);

                if (roles.IsFailure)
                    return Result.Failure<LoginResponse>(roles.Error);

                //Si el usuario no existe, lo creo en el idp con la contraseña generada aleatoria
                var result = await CreateUser(email, roles.Value, password, tenantIdResult.Value, cancellationToken);

                if (result.IsFailure)
                    return Result.Failure<LoginResponse>(result.Error);
            }
            else
            {
                //Si el usuario existe, cambio la contraseña por la generada aleatoria
                var changePasswordResult = await identityService.ForceChangePassword(tenantIdResult.Value, email, password, cancellationToken);

                if (changePasswordResult.IsFailure)
                    return Result.Failure<LoginResponse>(changePasswordResult.Error);
            }

            //Cuando el usuario fue creado/actualizado realizo el inicio de sesion
            var loginResult = await identityService.LoginAsync(tenantIdResult.Value, email, password, cancellationToken);

            if (loginResult.IsFailure)
                return Result.Failure<LoginResponse>(loginResult.Error);

            //Si el idp requiere el challenge lo realizo, en caso contrario retorno el access token generado
            if (string.IsNullOrWhiteSpace(loginResult.Value.ChallengeName) || string.IsNullOrWhiteSpace(loginResult.Value.Session))
                return Result.Success(loginResult.Value);

            if (!loginResult.Value.ChallengeName.Equals("NEW_PASSWORD_REQUIRED"))
                return Result.Failure<LoginResponse>(new Error("INVALID_CHALLENGE", "Not recognized challenge"));

            //Completo el challenge
            var respondChallengeResult = await identityService.RespondToAuthChallengeAsync(tenantIdResult.Value, loginResult.Value.Session, email, loginResult.Value.ChallengeName, new()
            {
                { "NEW_PASSWORD", password }
            }, cancellationToken);

            if (respondChallengeResult.IsFailure)
                return Result.Failure<LoginResponse>(respondChallengeResult.Error);

            return Result.Success(respondChallengeResult.Value);
        }
        catch(Exception ex)
        {
            logger.LogError(ex, "Error trying to convert legacy JWT into CognitoJWT");
            return Result.Failure<LoginResponse>(new Error("JWT_TRANSFORMATION_FAILED", "Error trying to convert JWT"));
        }
    }

    private Result<string[]> GetRolesFromClaims(ClaimsIdentity claimsIdentity)
    {
        //Obtengo y valido la existencia de los roles del claim
        var rolesClaim = claimsIdentity.FindFirst("roles");

        if (rolesClaim is null || string.IsNullOrWhiteSpace(rolesClaim.Value))
            return Result.Failure<string[]>(new Error("INVALID_TOKEN_CLAIMS", "Invalid token claims"));

        string trimmed = rolesClaim.Value.Trim('[', ']');
        string[] roles = trimmed.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);

        return Result.Success(roles);
    }

    private async Task<Result> CreateUser(string email, string[] roles, string password, string tenantId, CancellationToken cancellationToken)
    {
        //Descuento el usuario en la licencia
        var result = await licenseService.Consume(tenantId, cancellationToken);
        
        if (result.IsFailure)
            return Result.Failure(new Error("CONSUME_CREDIT_FAILED", "Credits consumption failed"));

        Error? error;
        try
        {
            //Si el usuario del CRM posee el rol "Administrator" tambien es administrador para nosotros
            var role = roles.Contains("Administrator") ? Domain.Enums.UserRole.Administrator : Domain.Enums.UserRole.Standard;

            var userEntity = User.Create(null, null, email, role, Domain.Enums.UserOrigin.Federated, User.DefaultPermissions);

            //Creo el usuario en el idp
            var userCreationResult = await identityService.CreateUsersAsync(tenantId, [userEntity], password, cancellationToken);

            if (userCreationResult.IsSuccess)
                return Result.Success();

            error = userCreationResult.Error;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unhandled error creating user");
            error = new Error("UNHANDLED_CREATING_USER", ex.Message);
        }

        await licenseService.Refund(tenantId, cancellationToken);
        return Result.Failure(error);
    }
}
