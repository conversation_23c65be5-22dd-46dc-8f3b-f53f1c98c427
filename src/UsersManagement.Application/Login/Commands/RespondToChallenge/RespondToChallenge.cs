﻿
using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Application.Common.Models;

namespace UsersManagement.Application.Login.Commands.RespondToChallenge;

public record RespondToChallengeCommand : IRequest<Result<LoginResponse>>
{
    public string Session { get; init; } = string.Empty;
    public string Email { get; init; } = string.Empty;
    public string ChallengeName { get; init; } = string.Empty;
    public Dictionary<string, string> Parameters { get; init; } = new();
}

public class RespondToChallengeCommandHandler(ITenantProvider tenantProvider, IIdentityService identityService) : IRequestHandler<RespondToChallengeCommand, Result<LoginResponse>>
{
    public async Task<Result<LoginResponse>> Handle(RespondToChallengeCommand request, CancellationToken cancellationToken)
    {
        var tenantId = tenantProvider.GetTenantId();
        return await identityService.RespondToAuthChallengeAsync(tenantId, request.Session, request.Email, request.ChallengeName, request.Parameters, cancellationToken);
    }
}
