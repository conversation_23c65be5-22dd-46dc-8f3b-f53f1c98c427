﻿using UsersManagement.Application.Login.Commands.RespondToChallenge;

namespace UsersManagement.Application.Login.Commands.UserLogin;

public class RespondToChallengeCommandValidator : AbstractValidator<RespondToChallengeCommand>
{
    public RespondToChallengeCommandValidator()
    {
        RuleFor(v => v.Email)
            .EmailAddress()
            .NotEmpty();

        RuleFor(v => v.ChallengeName)
            .NotEmpty();

        RuleFor(v => v.Parameters)
            .NotEmpty();

        RuleFor(v => v.Session)
            .NotEmpty();
    }
}
