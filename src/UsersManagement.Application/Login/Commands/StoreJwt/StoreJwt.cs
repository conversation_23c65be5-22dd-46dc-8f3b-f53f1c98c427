using Microsoft.Extensions.Logging;
using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Application.Common.Models;
using UsersManagement.Application.Common.Security;

namespace UsersManagement.Application.Login.Commands.StoreJwt;

public record StoreJwtCommand(string Jwt) : IRequest<Result<StoreJwtResponse>>;

public record StoreJwtResponse(string Key);

public class StoreJwtCommandHandler : IRequestHandler<StoreJwtCommand, Result<StoreJwtResponse>>
{
    private readonly ISessionService _sessionService;
    private readonly ITenantProvider _tenantProvider;
    private readonly IIdentityService _identityService;
    private readonly ISettingsService _settingsService;
    private readonly IExternalJwtValidator _externalJwtValidator;
    private readonly ILogger<StoreJwtCommand> _logger;

    public StoreJwtCommandHandler(ISessionService sessionService, ITenantProvider tenantProvider, IIdentityService identityService, ISettingsService settingsService, IExternalJwtValidator externalJwtValidator, ILogger<StoreJwtCommand> logger)
    {
        _sessionService = sessionService;
        _tenantProvider = tenantProvider;
        _identityService = identityService;
        _settingsService = settingsService;
        _externalJwtValidator = externalJwtValidator;
        _logger = logger;
    }

    public async Task<Result<StoreJwtResponse>> Handle(StoreJwtCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var tenantName = _tenantProvider.GetTenantName();
            var tenantCountry = _tenantProvider.GetTenantCountry();

            //Obtengo el tenantId
            var tenantIdResult = await _identityService.GetTenantIdByNameAndCountry(tenantName, tenantCountry, cancellationToken);

            if (tenantIdResult.IsFailure)
                return Result.Failure<StoreJwtResponse>(tenantIdResult.Error);

            //Obtengo el JWK
            var jwkResult = await _settingsService.GetJWKAsync(tenantIdResult.Value, cancellationToken);

            if (jwkResult.IsFailure)
                return Result.Failure<StoreJwtResponse>(jwkResult.Error);

            //Valido el JWT contra el JWK
            var jwtValidationResult = await _externalJwtValidator.ValidateJwtByJwkAsync(jwkResult.Value, request.Jwt);

            if (jwtValidationResult.IsFailure)
                return Result.Failure<StoreJwtResponse>(jwtValidationResult.Error);

            // Delegate JWT storage to the session service
            var result = await _sessionService.StoreJwtAsync(tenantIdResult.Value, request.Jwt, cancellationToken);
            
            if (result.IsFailure)
            {
                _logger.LogError("Failed to store JWT: {Error}", result.Error.Message);
                return Result.Failure<StoreJwtResponse>(result.Error);
            }
            
            // Return the key from the session service
            return Result.Success(new StoreJwtResponse(result.Value));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error trying to store JWT");
            return Result.Failure<StoreJwtResponse>(new Error("JWT_STORAGE_FAILED", "Error trying to store JWT"));
        }
    }
}
