﻿
using Microsoft.Extensions.Logging;
using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Application.Common.Models;
using UsersManagement.Domain.Errors;

namespace UsersManagement.Application.Login.Commands.RefreshToken;

public record RefreshTokenCommand : IRequest<Result<LoginResponse>>
{
    public string RefreshToken { get; init; } = string.Empty;
}

internal class RefreshTokenCommandHandler(IIdentityService identityService, ITenantProvider tenantProvider, IUser user, ILogger<RefreshTokenCommandHandler> logger) : IRequestHandler<RefreshTokenCommand, Result<LoginResponse>>
{
    public async Task<Result<LoginResponse>> Handle(RefreshTokenCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var tenantCountry = tenantProvider.GetTenantCountry();
            var tenantName = tenantProvider.GetTenantName();

            var tenantIdRestult = await identityService.GetTenantIdByNameAndCountry(tenantName, tenantCountry, cancellationToken);

            if (tenantIdRestult.IsFailure)
                return Result.Failure<LoginResponse>(tenantIdRestult.Error);

            return await identityService.RefreshTokenAsync(tenantIdRestult.Value, user.Username, request.RefreshToken, cancellationToken);
        }
        catch(Exception ex)
        {
            logger.LogError(ex, "Error refreshing token.");
            return Result.Failure<LoginResponse>(IdentityErrors.Cognito.REFRESH_GENERIC);
        }
    }
}
