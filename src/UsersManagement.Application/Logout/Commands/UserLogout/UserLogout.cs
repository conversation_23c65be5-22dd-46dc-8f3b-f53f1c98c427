﻿
using UsersManagement.Application.Common.Interfaces;

namespace UsersManagement.Application.Logout.Commands.UserLogout;

public record UserLogoutCommand : IRequest<Result>
{
    public string RefreshToken { get; init; } = string.Empty;
}


internal class UserLogoutCommandHandler(IIdentityService identityService, ITenantProvider tenantProvider) : IRequestHandler<UserLogoutCommand, Result>
{
    public async Task<Result> Handle(UserLogoutCommand request, CancellationToken cancellationToken)
    {
        
        var tenantName = tenantProvider.GetTenantName();
        var tenantCountry = tenantProvider.GetTenantCountry();
        
        var tenantIdResult = await identityService.GetTenantIdByNameAndCountry(tenantName, tenantCountry, cancellationToken);

        if (tenantIdResult.IsFailure)
            return Result.Failure(tenantIdResult.Error);

        return await identityService.RevokeTokenAsync(tenantIdResult.Value, request.RefreshToken, cancellationToken);
    }
}
