﻿using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.JsonWebTokens;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;
using UsersManagement.Application.Common.Interfaces;

namespace UsersManagement.Application.Common.Security;
internal class ExternalJwtValidator(ILogger<ExternalJwtValidator> logger) : IExternalJwtValidator
{
    public async Task<Result<ClaimsIdentity>> ValidateJwtByJwkAsync(string jwkjson, string token)
    {
        try
        {
            var jwks = new JsonWebKeySet(jwkjson);
            var jwk = jwks.Keys.FirstOrDefault();

            if (jwk is null)
                throw new Exception("Invalid JWK");

            var validationParameters = new TokenValidationParameters
            {
                IssuerSigningKey = jwk,
                ValidateIssuer = false,
                ValidateAudience = false,
                ValidateActor = false,
                ValidateLifetime = true
            };
            var handler = new JsonWebTokenHandler();
            var claims = await handler.ValidateTokenAsync(token, validationParameters);
            //var claims = new ClaimsPrincipal(new ClaimsIdentity(handler.ReadJwtToken(token).Claims));
            if (!claims.IsValid)
                return Result.Failure<ClaimsIdentity>(new Error("INVALID_TOKEN", "Invalid legacy token"));

            return Result.Success(claims.ClaimsIdentity);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error validating JWT");
            return Result.Failure<ClaimsIdentity>(new Error("JWT_VALIDATION_FAILED", "Error trying to validate JWT"));
        }
    }
}
