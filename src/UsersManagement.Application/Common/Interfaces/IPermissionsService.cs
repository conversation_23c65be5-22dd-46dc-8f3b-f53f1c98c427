﻿using UsersManagement.Domain.Entities;
namespace UsersManagement.Application.Common.Interfaces;

public interface IPermissionsService
{
    Task<Result<List<Permission>>> GetAllPermissions(string tenantId, CancellationToken cancellationToken);
    Task<Result<bool>> ExistsPermissions(string tenantId, List<string> permission, CancellationToken cancellationToken);
    Task<Result> AddPermissionUserCount(string tenantId, List<string> permissions, int userCount, CancellationToken cancellationToken);
}
