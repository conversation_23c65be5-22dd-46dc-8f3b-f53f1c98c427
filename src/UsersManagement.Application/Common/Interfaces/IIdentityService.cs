﻿using UsersManagement.Application.Common.Models;
using UsersManagement.Domain.DTO;
using UsersManagement.Domain.Entities;

namespace UsersManagement.Application.Common.Interfaces;

public interface IIdentityService
{
    Task<Result<LoginResponse>> LoginAsync(string tenantId, string email, string password, CancellationToken cancellationToken); 
    Task<Result<LoginResponse>> RespondToAuthChallengeAsync(string tenantId, string session, string email, string challengeName, Dictionary<string, string> parameters, CancellationToken cancellationToken);
    Task<Result<(string paginationToken, List<User> users)>> GetUsersAsync(string? paginationToken, int limit, CancellationToken cancellationToken);
    Task<Result<User>> GetUserAsync(string userEmail, CancellationToken cancellationToken);
    Task<Result<(int createdQty, string? errors)>> CreateUsersAsync(string tenantId, IList<User> users, string? temporaryPassword = null, CancellationToken cancellationToken = default);
    Task<Result<string>> GetTenantIdByNameAndCountry(string tenantName, string tenantCountry, CancellationToken cancellationToken);
    Task<Result> DeleteUserAsync(string email, CancellationToken cancellationToken);
    Task<Result> EditUserAsync(string email, UpdateUserRequest request, string tenantId, CancellationToken cancellationToken);
    Task<Result> RevokeTokenAsync(string tenantId, string token, CancellationToken cancellationToken);
    Task<Result<bool>> UserExists(string tenantId, string email, CancellationToken cancellationToken);
    Task<Result> ForceChangePassword(string tenantId, string email, string password, CancellationToken cancellationToken);
    Task<Result<LoginResponse>> RefreshTokenAsync(string tenantId, string email, string refreshToken, CancellationToken cancellationToken);
    Task<Result> ForgotPasswordAsync(string tenantId, string email, CancellationToken cancellationToken);
    Task<Result> ConfirmForgotPasswordAsync(string tenantId, string email, string password, string code, CancellationToken cancellationToken);

}
