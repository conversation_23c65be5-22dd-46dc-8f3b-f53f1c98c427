﻿using UsersManagement.Application.Common.Models;

namespace UsersManagement.Application.Common.Interfaces;
public interface ISettingsService
{
    Task<Result<string>> GetConfiguration(Settings setting, CancellationToken cancellationToken = default);
    Task<Result<string>> GetConfiguration(Settings setting, string tenantId, CancellationToken cancellationToken = default);

    Task<Result<string>> GetJWKAsync(string tenantId, CancellationToken cancellationToken = default);
}
