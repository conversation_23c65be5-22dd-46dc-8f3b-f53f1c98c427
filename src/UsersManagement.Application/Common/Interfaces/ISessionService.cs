using N5.Result;

namespace UsersManagement.Application.Common.Interfaces;

/// <summary>
/// Interface for managing user sessions
/// </summary>
public interface ISessionService
{
    /// <summary>
    /// Stores a JWT token with a TTL of 1 minute
    /// </summary>
    /// <param name="tenantId">Tenant Id</param>
    /// <param name="jwt">JWT token to store</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing the base64 encoded key if successful</returns>
    Task<Result<string>> StoreJwtAsync(string tenantId, string jwt, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Retrieves a JWT token by its key
    /// </summary>
    /// <param name="tenantId">Tenant Id</param>
    /// <param name="key">Base64 encoded key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing the JW<PERSON> token if successful</returns>
    Task<Result<string>> GetJwtAsync(string tenantId, string key, CancellationToken cancellationToken = default);
}
