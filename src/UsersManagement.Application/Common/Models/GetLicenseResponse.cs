﻿using UsersManagement.Domain.DTO;

namespace UsersManagement.Application.Common.Models;
public class GetLicenseResponse
{
    public int Total { get; private set; }
    public int Used { get; private set; }
    public int Free { get; private set; }

    public static GetLicenseResponse FromLicense(License license)
    {
        return new()
        {
            Free = license.Free,
            Total = license.Total,
            Used = license.Used
        };
    }
}
