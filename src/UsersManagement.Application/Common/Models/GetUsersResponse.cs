﻿using UsersManagement.Domain.Entities;
using UsersManagement.Domain.Enums;

namespace UsersManagement.Application.Common.Models;
public record GetUsersResponse(IEnumerable<UserDTO> Users, string Cursor);

public class UserDTO
{
    private UserDTO(string? name, string? surname, string email, UserRole role, bool confirmed, List<string> permissions)
    {
        Name = name;
        Surname = surname;
        Email = email;
        Role = role;
        Confirmed = confirmed;
        Permissions = permissions;
    }

    public string? Name { get; private set; }
    public string? Surname { get; private set; }
    public string Email { get; private set; }
    public UserRole Role { get; private set; }
    public bool Confirmed { get; private set; }
    public List<string> Permissions { get; private set; }
    public static UserDTO FromUser(User user)
    {
        return new UserDTO(
            user.Name,
            user.Surname, 
            user.Email, 
            user.Role,
            user.Confirmed,
            user.Permissions);
    }
}
