﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <RootNamespace>UsersManagement.Application</RootNamespace>
    <AssemblyName>UsersManagement.Application</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.GuardClauses" />
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" />
    <PackageReference Include="N5.Result" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" />
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleTo Include="UsersManagement.Application.UnitTests" />
    <InternalsVisibleTo Include="DynamicProxyGenAssembly2" />
    <ProjectReference Include="..\UsersManagement.Domain\UsersManagement.Domain.csproj" />
  </ItemGroup>

</Project>
