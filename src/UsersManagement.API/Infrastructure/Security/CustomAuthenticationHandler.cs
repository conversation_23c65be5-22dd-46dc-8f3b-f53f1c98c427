﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Options;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace UsersManagement.API.Infrastructure.Security;

public class CustomAuthenticationHandler(IOptionsMonitor<JwtBearerOptions> options, ILoggerFactory logger, UrlEncoder encoder) : JwtBearerHandler(options, logger, encoder)
{
    protected override Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        if (Context.Request.Path.Value?.Contains("swagger") ?? false)
            return Task.FromResult(AuthenticateResult.NoResult());

        if (!Context.Request.Headers.TryGetValue("Authorization", out var authorizationHeader))
            return Task.FromResult(AuthenticateResult.Fail("authorization is missing"));

        if (string.IsNullOrWhiteSpace(authorizationHeader.ToString()))
            return Task.FromResult(AuthenticateResult.Fail("header present but empty"));

        var token = authorizationHeader;

        var principal = GetClaims(token.ToString());

        return Task.FromResult(AuthenticateResult.Success(new AuthenticationTicket(principal, "CustomJwtBearer")));
    }

    private ClaimsPrincipal GetClaims(string Token)
    {
        var handler = new JwtSecurityTokenHandler();
        var token = handler.ReadToken(Token.Substring("Bearer ".Length).Trim()) as JwtSecurityToken;

        var claimsIdentity = new ClaimsIdentity(token!.Claims, "Token");
        var claimsPrincipal = new ClaimsPrincipal(claimsIdentity);

        return claimsPrincipal;
    }
}
