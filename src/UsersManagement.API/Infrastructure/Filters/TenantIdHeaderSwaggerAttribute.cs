﻿using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace UsersManagement.API.Infrastructure.Filters;

public abstract class HeaderSwaggerAttribute : IOperationFilter
{
    private OpenApiParameter? _openApiParameter;

    protected void AddParameter(OpenApiParameter apiParameter)
    {
        _openApiParameter = apiParameter;
    }

    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        operation.Parameters ??= new List<OpenApiParameter>();
        if (!operation.Parameters.Contains(_openApiParameter))
            operation.Parameters.Add(_openApiParameter);
    }
}

public class TenantIdHeaderSwaggerAttribute : HeaderSwaggerAttribute
{
    public TenantIdHeaderSwaggerAttribute()
    {
        AddParameter(new()
        {
            Name = "x-tenant-id",
            In = ParameterLocation.Header,
            Required = false,
            Schema = new OpenApiSchema
            {
                Type = "string",
                Enum = new List<IOpenApiAny>
            {
                new OpenApiString("us-east-1_gitriqEzP")
            }
            }
        });
    }
}

public class TenantNameHeaderSwaggerAttribute : HeaderSwaggerAttribute
{
    public TenantNameHeaderSwaggerAttribute()
    {
        AddParameter(new()
        {
            Name = "x-tenant-name",
            In = ParameterLocation.Header,
            Required = false,
            Schema = new OpenApiSchema
            {
                Type = "string",
                Enum = new List<IOpenApiAny>
                {
                    new OpenApiString("develop")
                }
            }
        });
    }
}

public class TenantCountryHeaderSwaggerAttribute : HeaderSwaggerAttribute
{
    public TenantCountryHeaderSwaggerAttribute()
    {
        AddParameter(new()
        {
            Name = "x-tenant-country",
            In = ParameterLocation.Header,
            Required = false,
            Schema = new OpenApiSchema
            {
                Type = "string",
                Enum = new List<IOpenApiAny>
                {
                    new OpenApiString("arg")
                }
            }
        });
    }
}
