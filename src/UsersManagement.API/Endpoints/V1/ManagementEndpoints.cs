﻿using Carter;
using Microsoft.AspNetCore.Http.HttpResults;
using N5.Result;
using UsersManagement.Application.Common.Models;
using UsersManagement.Application.Management.Commands.DeleteUser;
using UsersManagement.Application.Management.Commands.EditUser;
using UsersManagement.Application.Management.Commands.RegisterUsers;
using UsersManagement.Application.Management.Queries.GetLicenses;
using UsersManagement.Application.Management.Queries.GetPermissions;
using UsersManagement.Application.Management.Queries.GetUsers;
using UsersManagement.Domain.DTO;
using UsersManagement.Domain.Entities;

namespace UsersManagement.API.Endpoints.V1;

public class ManagementEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = VersionGroupProvider
           .GetRouteGroupBuilder(app, Application.Constants.Constants.Versions.v1)
           .MapGroup("management")
           .WithName(nameof(ManagementEndpoints))
           .WithTags("Management");

        group.MapGet("", GetUsers)
            .WithName(nameof(GetUsers))
            .WithOpenApi();

        group.MapPost("register", RegisterUser)
            .WithName(nameof(RegisterUser))
            .WithOpenApi();

        group.MapDelete("{email}", DeleteUser)
            .WithName(nameof(DeleteUser))
            .WithOpenApi();

        group.MapPatch("{email}", EditUser)
            .WithName(nameof(EditUser))
            .WithOpenApi();

        group.MapGet("license", GetLicense)
            .WithName(nameof(GetLicense))
            .WithOpenApi();

        group.MapGet("permissions", GetPermissions)
            .WithName(nameof(GetPermissions))
            .WithOpenApi();
    }

    public static async Task<Results<Ok<GetUsersResponse>, BadRequest>> GetUsers(string? cursor, ISender sender, CancellationToken cancellationToken, int limit = 10)
    {
        var users = await sender.Send(new GetUsersCommand { Cursor = cursor, Limit = limit }, cancellationToken);

        return users.IsSuccess ? TypedResults.Ok(users.Value) : TypedResults.BadRequest();
    }

    public static async Task<Results<Created, BadRequest<Error>>> RegisterUser(IList<UserRegistration> users, ISender sender, CancellationToken cancellationToken)
    {
        var result = await sender.Send(new RegisterUsersCommand { Users = users }, cancellationToken);

        return result.IsSuccess ? TypedResults.Created() : TypedResults.BadRequest(result.Error);
    }

    public static async Task<Results<Ok<GetLicenseResponse>, BadRequest<Error>>> GetLicense(ISender sender, CancellationToken cancellationToken)
    {
        var result = await sender.Send(new GetLicenseQuery());
        return result.IsSuccess ? TypedResults.Ok(result.Value) : TypedResults.BadRequest(result.Error);
    }

    public static async Task<Results<NoContent, BadRequest<Error>>> DeleteUser(string email, ISender sender, CancellationToken cancellationToken)
    {
        var result = await sender.Send(new DeleteUserCommand() { Email = email }, cancellationToken);
        return result.IsSuccess ? TypedResults.NoContent() : TypedResults.BadRequest(result.Error);
    }

    public static async Task<Results<NoContent, BadRequest<Error>>> EditUser(string email, UpdateUserRequest updateUserRequest, ISender sender, CancellationToken cancellationToken)
    {
        var result = await sender.Send(new EditUserCommand
        {
            Email = email,
            Request = updateUserRequest
        }, cancellationToken);

        return result.IsSuccess ? TypedResults.NoContent() : TypedResults.BadRequest(result.Error);
    }

    public static async Task<Results<Ok<List<Permission>>, BadRequest<Error>>> GetPermissions(ISender sender, CancellationToken cancellationToken)
    {
        var result = await sender.Send(new GetPermissionsQuery(), cancellationToken);
        return result.IsSuccess ? TypedResults.Ok(result.Value) : TypedResults.BadRequest(result.Error);
    }
}
