﻿using <PERSON>;
using Microsoft.AspNetCore.Http.HttpResults;
using N5.Result;
using UsersManagement.Application.Logout.Commands.UserLogout;

namespace UsersManagement.API.Endpoints.V1;

public class LogoutEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = VersionGroupProvider
           .GetRouteGroupBuilder(app, Application.Constants.Constants.Versions.v1)
           .MapGroup("logout")
           .WithName(nameof(LogoutEndpoints))
           .WithTags("Logout");

        group.MapPost("", Logout)
            .WithName(nameof(Logout))
            .WithOpenApi();
    }

    public static async Task<Results<NoContent, BadRequest<Error>>> Logout(UserLogoutCommand logoutCommand, ISender sender, CancellationToken cancellationToken)
    {
        var response = await sender.Send(logoutCommand, cancellationToken);

        return response.IsFailure ? TypedResults.BadRequest(response.Error) : TypedResults.NoContent();
    }
}
