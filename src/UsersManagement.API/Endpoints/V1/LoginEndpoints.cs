﻿using Carter;
using Microsoft.AspNetCore.Http.HttpResults;
using N5.Result;
using UsersManagement.Application.Common.Models;
using UsersManagement.Application.Login.Commands.ConfirmForgotPassword;
using UsersManagement.Application.Login.Commands.ExternalLogin;
using UsersManagement.Application.Login.Commands.ForgotPassword;
using UsersManagement.Application.Login.Commands.GenerateSession;
using UsersManagement.Application.Login.Commands.RefreshToken;
using UsersManagement.Application.Login.Commands.RespondToChallenge;
using UsersManagement.Application.Login.Commands.StoreJwt;
using UsersManagement.Application.Login.Commands.UserLogin;

namespace UsersManagement.API.Endpoints.V1;

public class LoginEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = VersionGroupProvider
           .GetRouteGroupBuilder(app, Application.Constants.Constants.Versions.v1)
           .MapGroup("login")
           .WithName(nameof(LoginEndpoints))
           .WithTags("Login");

        group.MapPost("", Login)
            .WithName(nameof(Login))
            .WithOpenApi();

        group.MapPost("respondtochallenge", RespondToChallenge)
            .WithName(nameof(RespondToChallenge))
            .WithOpenApi();

        group.MapPost("refresh", RefreshToken)
            .WithName(nameof(RefreshToken))
            .WithOpenApi();

        group.MapPost("external", External)
            .WithName(nameof(External))
            .WithOpenApi();

        group.MapPost("forgot_password", ForgotPassword)
            .WithName(nameof(ForgotPassword))
            .WithOpenApi();

        group.MapPost("forgot_password/confirm", ConfirmForgotPassword)
            .WithName(nameof(ConfirmForgotPassword))
            .WithOpenApi();

        group.MapPost("create_sesion", CreateSession)
            .WithName(nameof(CreateSession))
            .WithOpenApi();

        group.MapPost("retrieve_session", RetrieveSession)
            .WithName(nameof(RetrieveSession))
            .WithOpenApi();
    }

    public static async Task<Results<Ok<LoginResponse>, BadRequest<Error>>> Login(UserLoginCommand loginCommand, ISender sender, CancellationToken cancellationToken)
    {
        var response = await sender.Send(loginCommand, cancellationToken);

        return response.IsFailure ? TypedResults.BadRequest(response.Error) : TypedResults.Ok(response.Value);
    }

    public static async Task<Results<Ok<LoginResponse>, BadRequest<Error>>> RespondToChallenge(RespondToChallengeCommand respondToChallengeCommand, ISender sender, CancellationToken cancellationToken)
    {
        var response = await sender.Send(respondToChallengeCommand, cancellationToken);

        return response.IsFailure ? TypedResults.BadRequest(response.Error) : TypedResults.Ok(response.Value);
    }

    public static async Task<Results<Ok<LoginResponse>, BadRequest<Error>>> External(ExternalLoginCommand externalLoginCommand, ISender sender, CancellationToken cancellationToken)
    {
        var response = await sender.Send(externalLoginCommand, cancellationToken);

        return response.IsFailure ? TypedResults.BadRequest(response.Error) : TypedResults.Ok(response.Value);
    }

    public static async Task<Results<Ok<LoginResponse>, BadRequest<Error>>> RefreshToken(RefreshTokenCommand refreshTokenCommand, ISender sender, CancellationToken cancellationToken)
    {
        var response = await sender.Send(refreshTokenCommand, cancellationToken);

        return response.IsFailure ? TypedResults.BadRequest(response.Error) : TypedResults.Ok(response.Value);
    }

    public static async Task<Results<NoContent, BadRequest<Error>>> ForgotPassword(ForgotPasswordCommand command, ISender sender, CancellationToken cancellationToken)
    {
        var response = await sender.Send(command, cancellationToken);

        return response.IsFailure ? TypedResults.BadRequest(response.Error) : TypedResults.NoContent();
    }

    public static async Task<Results<NoContent, BadRequest<Error>>> ConfirmForgotPassword(ConfirmForgotPasswordCommand confirmForgotPasswordCommand, ISender sender, CancellationToken cancellationToken)
    {
        var result = await sender.Send(confirmForgotPasswordCommand, cancellationToken);

        return result.IsFailure ? TypedResults.BadRequest(result.Error) : TypedResults.NoContent();
    }

    public static async Task<Results<Ok<StoreJwtResponse>, BadRequest<Error>>> CreateSession(StoreJwtCommand command, ISender sender, CancellationToken cancellationToken)
    {
        var result = await sender.Send(command, cancellationToken);

        return result.IsFailure ? TypedResults.BadRequest(result.Error) : TypedResults.Ok(result.Value);
    }

    public static async Task<Results<Ok<LoginResponse>, BadRequest<Error>>> RetrieveSession(GenerateSessionCommand command, ISender sender, CancellationToken cancellationToken)
    {
        var result = await sender.Send(command, cancellationToken);

        return result.IsFailure ? TypedResults.BadRequest(result.Error) : TypedResults.Ok(result.Value);
    }
}
