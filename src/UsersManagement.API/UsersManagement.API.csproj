﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <RootNamespace>UsersManagement.API</RootNamespace>
    <AssemblyName>UsersManagement.API</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\UsersManagement.Application\UsersManagement.Application.csproj" />
    <ProjectReference Include="..\UsersManagement.Infrastructure\UsersManagement.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Carter" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" />
    <PackageReference Include="FluentValidation.AspNetCore" />
    <PackageReference Include="N5.Platform.Instrumentation" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
  </ItemGroup>


  <!-- Auto-generated Open API specification and Angular TypeScript clients -->
  <PropertyGroup>
    <RunPostBuildEvent>OnBuildSuccess</RunPostBuildEvent>
  </PropertyGroup>
</Project>
