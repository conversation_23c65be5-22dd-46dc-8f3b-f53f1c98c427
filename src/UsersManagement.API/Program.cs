using Carter;
using N5.Platform.Instrumentation;
using UsersManagement.Application.Constants;

var builder = WebApplication.CreateBuilder(args);

builder.AddInstrumentations();
builder.Services.AddApplicationServices();
builder.Services.AddInfrastructureServices(builder.Configuration);
builder.Services.AddWebServices();

var app = builder.Build();

app.UseSwagger(c =>
{
    c.RouteTemplate = $"{Constants.API_PREFIX}/swagger/{{documentName}}/swagger.json";
});

app.UseSwaggerUI(c =>
{
    c.RoutePrefix = $"{Constants.API_PREFIX}/swagger";
});

app.AddInstrumentations();
app.UseAuthorization();
app.UseAuthentication();
app.MapCarter();
app.MapHealthChecks("/health");
app.UseExceptionHandler(options => { });

app.Run();

public partial class Program { }
