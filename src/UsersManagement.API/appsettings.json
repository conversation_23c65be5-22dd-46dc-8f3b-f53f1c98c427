{"Metrics": {"EnabledMetrics": {"Microsoft.AspNetCore.*": true, "System.*": false, "N5.*": true}}, "Logging": {"LogLevel": {"Default": "Error", "Microsoft": "Error"}, "Console": {"LogLevel": {"Default": "Error", "Microsoft": "Error", "Microsoft.Hosting.Lifetime": "Error"}, "FormatterName": "json", "FormatterOptions": {"SingleLine": true, "IncludeScopes": true, "TimestampFormat": "HH:mm:ss ", "UseUtcTimestamp": true, "JsonWriterOptions": {"Indented": false}}}}, "AllowedHosts": "*"}