﻿using UsersManagement.Application.Common.Interfaces;

namespace UsersManagement.API.Services;

public class TenantProvider
(
    IHttpContextAccessor httpContextAccessor
) : ITenantProvider
{
    public const string TenantIdHeader = "x-tenant-id";
    public const string TenantNameHeader = "x-tenant-name";
    public const string TenantCountryHeader = "x-tenant-country";

    private string GetHeaderOrThrow(string key)
    {
        if (httpContextAccessor.HttpContext is null)
            throw new NotSupportedException("Wrong application context");

        if (!httpContextAccessor.HttpContext
            .Request
            .Headers.TryGetValue(key, out var tenantIdHeader))
            throw new ApplicationException("Header not present");

        return tenantIdHeader.ToString();
    }

    public string GetTenantCountry() => GetHeaderOrThrow(TenantCountryHeader);

    public string GetTenantId() => GetHeaderOrThrow(TenantIdHeader);

    public string GetTenantName() => GetHeaderOrThrow(TenantNameHeader);
}
