﻿using N5.Result;

namespace UsersManagement.Domain.Errors;
public partial class IdentityErrors
{
    public static class Cognito
    {
        public static Error DISABLED_USER = new Error(nameof(DISABLED_USER).ToLower(), "User is disabled, please contact an administrator.");
        public static Error INCORRECT_USER_PASS = new Error(nameof(INCORRECT_USER_PASS).ToLower(), "Incorrect username or password.");
        public static Error LOGIN_GENERIC = new Error(nameof(LOGIN_GENERIC).ToLower(), "Error trying to authenticate user, please try again later.");
        public static Error INVALID_PASS_FORMAT = new Error(nameof(INVALID_PASS_FORMAT).ToLower(), "Invalid password format.");
        public static Error CHALLENGE_GENERIC = new Error(nameof(CHALLENGE_GENERIC).ToLower(), "Error trying to refresh password, please try again later.");
   
        public static Error USER_POOL_NOT_FOUND = new Error(nameof(USER_POOL_NOT_FOUND).ToLower(), "User pool not found.");
        public static Error CLIENT_ID_EMPTY = new Error(nameof(CLIENT_ID_EMPTY).ToLower(), "Cognito clientid was empty.");
        public static Error CLIENT_SECRET_EMPTY = new Error(nameof(CLIENT_SECRET_EMPTY).ToLower(), "Cognito client secret was empty.");

        public static Error LICENSE_ERROR = new Error(nameof(LICENSE_ERROR).ToLower(), "Error attempting to obtain license.");
        public static Error TOO_MUCH_LICENSES = new Error(nameof(TOO_MUCH_LICENSES), "Attempting to create more users than avaliable licenses.");
        public static Error UPDATE_LICENCES_COUNT = new Error(nameof(UPDATE_LICENCES_COUNT), "Failed to update license count.");

        public static Error DELETE_FAIL = new Error(nameof(DELETE_FAIL), "Failed to delete user");

        public static Error REVOKE_FAIL = new Error(nameof(REVOKE_FAIL), "Failed to revoke token");
        
        public static Error ERROR_OBTAINING_USER = new Error(nameof(ERROR_OBTAINING_USER).ToLower(), "Error obtaining user from user pool.");

        public static Error FORCE_PASSWORD_CHANGE_FAIL = new Error(nameof(FORCE_PASSWORD_CHANGE_FAIL).ToLower(), "Error changing password.");

        public static Error UPDATE_USER_FAIL = new Error(nameof(UPDATE_USER_FAIL).ToLower(), "Error updating user.");
        public static Error UPDATE_USER_NOTHING_TO_UPDATE = new Error(nameof(UPDATE_USER_NOTHING_TO_UPDATE).ToLower(), "Nothing to update.");

        public static Error REFRESH_GENERIC = new Error(nameof(REFRESH_GENERIC).ToLower(), "Error refreshing token.");

        public static Error FORGOT_GENERIC = new Error(nameof(FORGOT_GENERIC).ToLower(), "Error sending confirmation email.");

        public static Error CONFIRM_FORGOT_GENERIC = new Error(nameof(CONFIRM_FORGOT_GENERIC).ToLower(), "Error trying to confirm forgot password.");
        public static Error CONFIRM_FORGOT_EXPIRED_CODE = new Error(nameof(CONFIRM_FORGOT_EXPIRED_CODE).ToLower(), "The reset code is expired, please get a new one.");
        public static Error CONFIRM_FORGOT_CODE_MISMATCH = new Error(nameof(CONFIRM_FORGOT_CODE_MISMATCH).ToLower(), "The reset code is wrong.");
        public static Error CONFIRM_FORGOT_PASSWORD_HISTORY = new Error(nameof(CONFIRM_FORGOT_PASSWORD_HISTORY).ToLower(), "The password was used before, please try a new one.");
    }
}
