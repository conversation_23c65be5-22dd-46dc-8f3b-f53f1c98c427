﻿using UsersManagement.Domain.Enums;

namespace UsersManagement.Domain.Entities;
public class User
{
    private User(string? name, string? surname, string email, UserRole role, bool confirmed, UserOrigin origin, List<string> permissions)
    {
        Name = name;
        Surname = surname;
        Email = email;
        Role = role;
        Confirmed = confirmed;
        Origin = origin;
        Permissions = permissions;
    }
    public string? Name { get; private set; }
    public string? Surname { get; private set; }
    public string Email { get; private set; }
    public UserRole Role { get; private set; }
    public bool Confirmed { get; private set; }
    public UserOrigin Origin { get; private set; }
    public List<string> Permissions { get; private set; }

    public static User Create(string? name, string? surname, string email, UserRole role, UserOrigin origin, List<string> permissions)
    {
        return new User(name, surname, email, role, false, origin, permissions);
    }

    public static User Map(string? name, string? surname, string email, UserRole role, bool confirmed, UserOrigin origin, List<string> permissions)
    {
        return new User(name, surname, email, role, confirmed, origin, permissions);
    }

    public static List<string> DefaultPermissions => ["GREY"];
}
