﻿using System.Text.Json.Serialization;

namespace UsersManagement.Domain.DTO;
public class License
{
    public License(string tenantId, int total, int used, DateTime expirationDate)
    {
        TenantId = tenantId;
        Total = total;
        Used = used;
        ExpirationDate = expirationDate;
    }

    [JsonPropertyName("LicensesQuantity")]
    public int Total { get; private set; }

    [JsonPropertyName("UsedLicensesQuantity")]
    public int Used { get; private set; }

    public int Free => Total - Used;
    
    public string TenantId { get; private set; }
    
    [JsonPropertyName("ValidityPeriodEnd")]
    public DateTime ExpirationDate { get; private set; }
}
