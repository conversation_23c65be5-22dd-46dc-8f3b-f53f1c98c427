﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{6ED356A7-8B47-4613-AD01-C85CF28491BD}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{664D406C-2F83-48F0-BFC3-408D5CB53C65}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{E2DA20AA-28D1-455C-BF50-C49A8F831633}"
	ProjectSection(SolutionItems) = preProject
		..\.editorconfig = ..\.editorconfig
		..\.gitignore = ..\.gitignore
		..\CodeCoverage.runsettings = ..\CodeCoverage.runsettings
		..\Directory.Build.props = ..\Directory.Build.props
		..\Directory.Packages.props = ..\Directory.Packages.props
		..\README.md = ..\README.md
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UsersManagement.API", "UsersManagement.API\UsersManagement.API.csproj", "{2919228D-4341-4D82-BBA1-97D8CE8117AA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UsersManagement.Application", "UsersManagement.Application\UsersManagement.Application.csproj", "{58C8C1E2-9E28-44E1-A63C-EFAC2C0F05DC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UsersManagement.Domain", "UsersManagement.Domain\UsersManagement.Domain.csproj", "{E9E1C42E-28FF-4A67-A2A7-27A58A6FE408}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UsersManagement.Infrastructure", "UsersManagement.Infrastructure\UsersManagement.Infrastructure.csproj", "{12912D1D-388A-4B3C-839C-2E3C50DFCDFD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Application.FunctionalTests", "..\tests\Application.FunctionalTests\Application.FunctionalTests.csproj", "{FAF00DDB-8C73-4F43-BB06-EA9712642C1B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Application.UnitTests", "..\tests\Application.UnitTests\Application.UnitTests.csproj", "{6D9AD9B5-7F3D-4FF6-9C3B-11B606C101BD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Domain.UnitTests", "..\tests\Domain.UnitTests\Domain.UnitTests.csproj", "{A3639500-00D7-4CCA-AC0E-4DDC7EDF7765}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Infrastructure.UnitTests", "..\tests\Infrastructure.UnitTests\Infrastructure.UnitTests.csproj", "{003187C1-12D8-4F9E-8A22-4325CE2E36FA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Infrastructure.IntegrationTests", "..\tests\Infrastructure.IntegrationTests\Infrastructure.IntegrationTests.csproj", "{097D146E-D01B-4193-8751-9E640DE944EE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "API.UnitTests", "..\tests\API.UnitTests\API.UnitTests.csproj", "{3D26CD23-8A4D-4B29-9C4D-F663A0F7EF12}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{2919228D-4341-4D82-BBA1-97D8CE8117AA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2919228D-4341-4D82-BBA1-97D8CE8117AA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2919228D-4341-4D82-BBA1-97D8CE8117AA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2919228D-4341-4D82-BBA1-97D8CE8117AA}.Release|Any CPU.Build.0 = Release|Any CPU
		{58C8C1E2-9E28-44E1-A63C-EFAC2C0F05DC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{58C8C1E2-9E28-44E1-A63C-EFAC2C0F05DC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{58C8C1E2-9E28-44E1-A63C-EFAC2C0F05DC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{58C8C1E2-9E28-44E1-A63C-EFAC2C0F05DC}.Release|Any CPU.Build.0 = Release|Any CPU
		{E9E1C42E-28FF-4A67-A2A7-27A58A6FE408}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E9E1C42E-28FF-4A67-A2A7-27A58A6FE408}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E9E1C42E-28FF-4A67-A2A7-27A58A6FE408}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E9E1C42E-28FF-4A67-A2A7-27A58A6FE408}.Release|Any CPU.Build.0 = Release|Any CPU
		{12912D1D-388A-4B3C-839C-2E3C50DFCDFD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{12912D1D-388A-4B3C-839C-2E3C50DFCDFD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{12912D1D-388A-4B3C-839C-2E3C50DFCDFD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{12912D1D-388A-4B3C-839C-2E3C50DFCDFD}.Release|Any CPU.Build.0 = Release|Any CPU
		{FAF00DDB-8C73-4F43-BB06-EA9712642C1B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FAF00DDB-8C73-4F43-BB06-EA9712642C1B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FAF00DDB-8C73-4F43-BB06-EA9712642C1B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FAF00DDB-8C73-4F43-BB06-EA9712642C1B}.Release|Any CPU.Build.0 = Release|Any CPU
		{6D9AD9B5-7F3D-4FF6-9C3B-11B606C101BD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6D9AD9B5-7F3D-4FF6-9C3B-11B606C101BD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6D9AD9B5-7F3D-4FF6-9C3B-11B606C101BD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6D9AD9B5-7F3D-4FF6-9C3B-11B606C101BD}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3639500-00D7-4CCA-AC0E-4DDC7EDF7765}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3639500-00D7-4CCA-AC0E-4DDC7EDF7765}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3639500-00D7-4CCA-AC0E-4DDC7EDF7765}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3639500-00D7-4CCA-AC0E-4DDC7EDF7765}.Release|Any CPU.Build.0 = Release|Any CPU
		{003187C1-12D8-4F9E-8A22-4325CE2E36FA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{003187C1-12D8-4F9E-8A22-4325CE2E36FA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{003187C1-12D8-4F9E-8A22-4325CE2E36FA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{003187C1-12D8-4F9E-8A22-4325CE2E36FA}.Release|Any CPU.Build.0 = Release|Any CPU
		{097D146E-D01B-4193-8751-9E640DE944EE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{097D146E-D01B-4193-8751-9E640DE944EE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{097D146E-D01B-4193-8751-9E640DE944EE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{097D146E-D01B-4193-8751-9E640DE944EE}.Release|Any CPU.Build.0 = Release|Any CPU
		{3D26CD23-8A4D-4B29-9C4D-F663A0F7EF12}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3D26CD23-8A4D-4B29-9C4D-F663A0F7EF12}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3D26CD23-8A4D-4B29-9C4D-F663A0F7EF12}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3D26CD23-8A4D-4B29-9C4D-F663A0F7EF12}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{2919228D-4341-4D82-BBA1-97D8CE8117AA} = {6ED356A7-8B47-4613-AD01-C85CF28491BD}
		{58C8C1E2-9E28-44E1-A63C-EFAC2C0F05DC} = {6ED356A7-8B47-4613-AD01-C85CF28491BD}
		{E9E1C42E-28FF-4A67-A2A7-27A58A6FE408} = {6ED356A7-8B47-4613-AD01-C85CF28491BD}
		{12912D1D-388A-4B3C-839C-2E3C50DFCDFD} = {6ED356A7-8B47-4613-AD01-C85CF28491BD}
		{FAF00DDB-8C73-4F43-BB06-EA9712642C1B} = {664D406C-2F83-48F0-BFC3-408D5CB53C65}
		{6D9AD9B5-7F3D-4FF6-9C3B-11B606C101BD} = {664D406C-2F83-48F0-BFC3-408D5CB53C65}
		{A3639500-00D7-4CCA-AC0E-4DDC7EDF7765} = {664D406C-2F83-48F0-BFC3-408D5CB53C65}
		{003187C1-12D8-4F9E-8A22-4325CE2E36FA} = {664D406C-2F83-48F0-BFC3-408D5CB53C65}
		{097D146E-D01B-4193-8751-9E640DE944EE} = {664D406C-2F83-48F0-BFC3-408D5CB53C65}
		{3D26CD23-8A4D-4B29-9C4D-F663A0F7EF12} = {664D406C-2F83-48F0-BFC3-408D5CB53C65}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {3CB609D9-5D54-4C11-A371-DAAC8B74E430}
	EndGlobalSection
EndGlobal
