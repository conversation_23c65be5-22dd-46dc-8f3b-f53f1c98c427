﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <RootNamespace>UsersManagement.Infrastructure</RootNamespace>
    <AssemblyName>UsersManagement.Infrastructure</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AWSSDK.CognitoIdentityProvider" />
    <PackageReference Include="AWSSDK.DynamoDBv2" />
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" />
    <PackageReference Include="AWSSDK.SecurityToken" />
    <PackageReference Include="AWSSDK.SSO" />
    <PackageReference Include="AWSSDK.SSOOIDC" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" />
    <PackageReference Include="Refit" />
    <PackageReference Include="Refit.HttpClientFactory" />
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleTo Include="UsersManagement.Infrastructure.UnitTests" />
    <InternalsVisibleTo Include="DynamicProxyGenAssembly2" />
    <ProjectReference Include="..\UsersManagement.Application\UsersManagement.Application.csproj" />
  </ItemGroup>

</Project>
