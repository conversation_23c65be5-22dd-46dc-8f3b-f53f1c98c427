﻿using Amazon.CognitoIdentityProvider;
using Microsoft.Extensions.Configuration;
using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Infrastructure.Identity;
using UsersManagement.Infrastructure.Settings;
using Refit;
using Amazon.DynamoDBv2;
using UsersManagement.Infrastructure.Licenses;
using UsersManagement.Infrastructure.DynamoDB.Abstractions;
using UsersManagement.Infrastructure.DynamoDB;
using UsersManagement.Infrastructure.Permissions;
using UsersManagement.Infrastructure.Sessions;

namespace Microsoft.Extensions.DependencyInjection;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddMemoryCache();

        services.AddScoped<ISettingsService, SettingsService>();
        services.AddScoped<IPermissionsService, PermissionsService>();

        services.AddRefitClient<ISettingsApi>()
            .ConfigureHttpClient(c => c.BaseAddress = new Uri(configuration.GetRequiredSection("CONFIG_SERVICE_URL").Value!));

        services.AddRefitClient<ILicenseApi>()
            .ConfigureHttpClient(c => c.BaseAddress = new Uri(configuration.GetRequiredSection("LICENSE_SERVICE_URL").Value!));

        services.AddTransient<IIdentityService, IdentityService>();
        services.AddAWSService<IAmazonCognitoIdentityProvider>();

        services.AddTransient<ILicenseService, LicenseService>();
        services
            .AddAWSService<IAmazonDynamoDB>()
            .AddSingleton<IDynamoDB, DynamoDB>();

        services.AddSingleton<IPasswordGenerator, CognitoPasswordGenerator>();
        services.AddScoped<ISessionService, SessionService>();

        return services;
    }
}
