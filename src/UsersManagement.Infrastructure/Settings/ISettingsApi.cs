﻿using Refit;

namespace UsersManagement.Infrastructure.Settings;

public interface ISettingsApi
{
    [Headers("Content-Type: application/json;charset=utf-8")]
    [Get("/api/v1/settings/{settingName}")]
    Task<KeyValuePair<string, string>> GetConfig([HeaderCollection] IDictionary<string, string> headers, [AliasAs("settingName")] string settingName, CancellationToken cancellationToken = default);

    [Headers("Content-Type: application/json;charset=utf-8")]
    [Get("/api/v1/settings/JWK")]
    Task<string?> GetJWK([HeaderCollection] IDictionary<string, string> headers, CancellationToken cancellationToken);
}
