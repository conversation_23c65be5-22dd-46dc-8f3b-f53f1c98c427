﻿using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using N5.Result;
using UsersManagement.Application.Common.Interfaces;

namespace UsersManagement.Infrastructure.Settings;

internal class SettingsService(
    IConfiguration configuration,
    ISettingsApi settingsApi,
    ITenantProvider tenantProvider,
    IMemoryCache cache,
    ILogger<SettingsService> logger
) : ISettingsService
{
    private static readonly int _timeoutMiliseconds = Convert.ToInt16(TimeSpan.FromSeconds(10).TotalMilliseconds);

    private static CancellationToken GetCancellationTokenWithTimeout(CancellationToken cancellationToken)
    {
        var tokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        tokenSource.CancelAfter(_timeoutMiliseconds);
        return tokenSource.Token;
    }

    private static string GetCacheKey(Application.Common.Models.Settings setting, string tenantId)
        => $"{setting}-{tenantId}";

    private static string GetJWKCacheKey(string tenantId)
        => $"JWK-{tenantId}";

    public Task<Result<string>> GetConfiguration(Application.Common.Models.Settings setting, CancellationToken cancellationToken = default)
        => GetConfiguration(setting, tenantProvider.GetTenantId(), cancellationToken);

    public async Task<Result<string>> GetConfiguration(Application.Common.Models.Settings setting, string tenantId, CancellationToken cancellationToken = default)
    {
        var cacheKey = GetCacheKey(setting, tenantId);
        try
        {
            var cachedSetting = await cache.GetOrCreateAsync(cacheKey, async (entry) =>
            {
                var settingValue = configuration.GetSection(setting.ToString()).Value;

                if (settingValue is not null)
                    return settingValue;

                var headers = new Dictionary<string, string> { { "X-Tenant-Id", tenantId } };
                var config = await settingsApi.GetConfig(headers, setting.ToString(), GetCancellationTokenWithTimeout(cancellationToken));

                return config.Value;
            });

            if (cachedSetting is not null)
                return Result.Success(cachedSetting);
            else
                return Result.Failure<string>(new Error("SettingsService.GetConfiguration", "Error while attempting to obtain setting"));

        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error while attempting to obtain setting");
            return Result.Failure<string>(new Error("SettingsService.GetConfiguration", "Error while attempting to obtain setting"));
        }
    }

    public async Task<Result<string>> GetJWKAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        var cacheKey = GetJWKCacheKey(tenantId);
        
        try
        {
            var cachedJWK = await cache.GetOrCreateAsync(cacheKey, async (entry) =>
            {
                var headers = new Dictionary<string, string> { { "X-Tenant-Id", tenantId } };
                var config = await settingsApi.GetJWK(headers, GetCancellationTokenWithTimeout(cancellationToken));

                return config;
            }, new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1)
            });

            if (!string.IsNullOrWhiteSpace(cachedJWK))
                return Result.Success(cachedJWK);
            else
                return Result.Failure<string>(new Error("SettingsService.GetJWKAsync", "Error while attempting to obtain JWK"));

        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error while attempting to obtain JWK");
            return Result.Failure<string>(new Error("SettingsService.GetJWKAsync", "Error while attempting to obtain JWK"));
        }
    }
}
