﻿using System.Security.Cryptography;
using System.Text;
using Amazon.CognitoIdentityProvider;
using Amazon.CognitoIdentityProvider.Model;
using Microsoft.Extensions.Logging;
using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Application.Common.Models;
using UsersManagement.Domain.Enums;
using UsersManagement.Domain.Errors;
using System.Diagnostics;
using UsersManagement.Domain.Entities;
using UsersManagement.Domain.DTO;

namespace UsersManagement.Infrastructure.Identity;
internal class IdentityService(
    IAmazonCognitoIdentityProvider amazonCognitoIdentityProvider,
    ISettingsService settingsService,
    ITenantProvider tenantProvider,
    ILogger<IdentityService> logger
) : IIdentityService
{
    public async Task<Result<LoginResponse>> LoginAsync(string tenantId, string email, string password, CancellationToken cancellationToken)
    {
        try
        {
            Activity.Current?.SetTag("platform.auth.user", email);
            var clientCredentialsResult = await GetClientCredentials(tenantId, cancellationToken);

            if (clientCredentialsResult.IsFailure)
            {
                logger.LogError("Error obtaining credentials from settings");
                return Result.Failure<LoginResponse>(IdentityErrors.Cognito.LOGIN_GENERIC);
            }

            (var clientId, var clientSecret) = clientCredentialsResult.Value;
            var secretHash = CreateSecretHash(email, clientId, clientSecret);

            var authParameters = new Dictionary<string, string>
            {
                {"USERNAME", email },
                {"PASSWORD", password },
                {"SECRET_HASH", secretHash }
            };

            var response = await amazonCognitoIdentityProvider.AdminInitiateAuthAsync(new()
            {
                AuthFlow = AuthFlowType.ADMIN_NO_SRP_AUTH,
                ClientId = clientId,
                UserPoolId = tenantId,
                AuthParameters = authParameters
            });

            return Result.Success(new LoginResponse(
                response.ChallengeName?.Value,
                response.Session,
                tenantId,
                response.AuthenticationResult?.AccessToken,
                response.AuthenticationResult?.RefreshToken));
        }
        catch (Exception ex)
        {
            if (ex is NotAuthorizedException)
            {
                if (ex.Message.ToLower().Contains("disabled"))
                    return Result.Failure<LoginResponse>(IdentityErrors.Cognito.DISABLED_USER);

                if (ex.Message.ToLower().Contains("incorrect"))
                    return Result.Failure<LoginResponse>(IdentityErrors.Cognito.INCORRECT_USER_PASS);
            }

            logger.LogError(ex, "Error attempting to authenticate user");
            return Result.Failure<LoginResponse>(IdentityErrors.Cognito.LOGIN_GENERIC);
        }
    }

    public async Task<Result<LoginResponse>> RespondToAuthChallengeAsync(string tenantId, string session, string email, string challengeName, Dictionary<string, string> parameters, CancellationToken cancellationToken)
    {
        try
        {
            Activity.Current?.SetTag("platform.auth.user", email).SetTag("platform.auth.challenge_name", challengeName);

            var clientCredentialsResult = await GetClientCredentials(tenantId, cancellationToken);

            if (clientCredentialsResult.IsFailure)
                return Result.Failure<LoginResponse>(clientCredentialsResult.Error);

            (var clientId, var clientSecret) = clientCredentialsResult.Value;
            var secretHash = CreateSecretHash(email, clientId, clientSecret);

            var challengeResponses = CognitoChallengeValidator.ValidateAndFilterKeys(challengeName, parameters);
            challengeResponses.Add("USERNAME", email);
            challengeResponses.Add("SECRET_HASH", secretHash);

            var response = await amazonCognitoIdentityProvider.AdminRespondToAuthChallengeAsync(new()
            {
                ChallengeName = ChallengeNameType.FindValue(challengeName),
                ClientId = clientId,
                UserPoolId = tenantId,
                Session = session,
                ChallengeResponses = challengeResponses
            });

            return Result.Success(new LoginResponse(
                response.ChallengeName?.Value,
                response.Session,
                tenantId,
                response.AuthenticationResult?.AccessToken,
                response.AuthenticationResult?.RefreshToken));
        }
        catch (Exception ex)
        {
            if (ex is InvalidPasswordException)
                return Result.Failure<LoginResponse>(IdentityErrors.Cognito.INVALID_PASS_FORMAT);

            return Result.Failure<LoginResponse>(IdentityErrors.Cognito.CHALLENGE_GENERIC);
        }
    }

    public async Task<Result<(string paginationToken, List<User> users)>> GetUsersAsync(string? paginationToken, int limit, CancellationToken cancellationToken)
    {
        try
        {
            var users = await amazonCognitoIdentityProvider.ListUsersAsync(new ListUsersRequest
            {
                UserPoolId = tenantProvider.GetTenantId(),
                PaginationToken = paginationToken,
                Limit = limit
            });

            //TODO: cuando se implemente el micro de licencias/funcione el cambio de estado revisar esta logica
            var status_attributes = users.Users
                .Select(x =>
                    User.Map(
                        x.Attributes.FirstOrDefault(x => x.Name == "name")?.Value,
                        x.Attributes.FirstOrDefault(x => x.Name == "family_name")?.Value,
                        x.Attributes.First(x => x.Name == "email").Value,
                        Enum.Parse<UserRole>(x.Attributes.First(x => x.Name == CognitoConstants.Attributes.ROLE).Value),
                        x.UserStatus == UserStatusType.CONFIRMED && x.Enabled,
                        Enum.Parse<UserOrigin>(x.Attributes.FirstOrDefault(x => x.Name == CognitoConstants.Attributes.ORIGIN)?.Value ?? UserOrigin.Standalone.ToString()),
                        x.Attributes.FirstOrDefault(x => x.Name == CognitoConstants.Attributes.PERMISSIONS)?.Value.Split(',').ToList() ?? new()))
                .ToList();

            return Result.Success((users.PaginationToken, status_attributes));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving users");
            return Result.Failure<(string paginationToken, List<User> users)>(new Error("IdentityService.GetUsersAsync", "Error retrieving users"));
        }
    }

    public async Task<Result<User>> GetUserAsync(string email, CancellationToken cancellationToken)
    {
        try
        {
            var user = await amazonCognitoIdentityProvider.AdminGetUserAsync(new AdminGetUserRequest()
            {
                Username = email,
                UserPoolId = tenantProvider.GetTenantId()
            });

            var userEntity = User.Map(user.UserAttributes.FirstOrDefault(x => x.Name == "name")?.Value,
                user.UserAttributes.FirstOrDefault(x => x.Name == "family_name")?.Value,
                user.UserAttributes.First(x => x.Name == "email").Value,
                Enum.Parse<UserRole>(user.UserAttributes.First(x => x.Name == CognitoConstants.Attributes.ROLE).Value),
                user.UserStatus == UserStatusType.CONFIRMED && user.Enabled,
                Enum.Parse<UserOrigin>(user.UserAttributes.FirstOrDefault(x => x.Name == CognitoConstants.Attributes.ORIGIN)?.Value ?? UserOrigin.Standalone.ToString()),
                user.UserAttributes.FirstOrDefault(x => x.Name == CognitoConstants.Attributes.PERMISSIONS)?.Value.Split(',').ToList() ?? new());

            return Result.Success(userEntity);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving user");
            return Result.Failure<User>(new Error("IdentityService.GetUserAsync", "Error retrieving user"));
        }
    }

    public async Task<Result<(int createdQty, string? errors)>> CreateUsersAsync(string tenantId, IList<User> users, string? temporaryPassword = null, CancellationToken cancellationToken = default)
    {
        var tasks = users.Select(async x =>
        {
            try
            {
                var response = await amazonCognitoIdentityProvider.AdminCreateUserAsync(new AdminCreateUserRequest
                {
                    UserAttributes = new()
                    {
                        new AttributeType
                        {
                            Name = "email",
                            Value = x.Email
                        },
                        new AttributeType
                        {
                            Name = "email_verified",
                            Value = "true"
                        },
                        new AttributeType
                        {
                            Name = "name",
                            Value = x.Name ?? string.Empty
                        },
                        new AttributeType
                        {
                            Name = "family_name",
                            Value = x.Surname ?? string.Empty
                        },
                        new AttributeType
                        {
                            Name = CognitoConstants.Attributes.ROLE,
                            Value = x.Role.ToString()
                        },
                        new AttributeType
                        {
                            Name = CognitoConstants.Attributes.ORIGIN,
                            Value = x.Origin.ToString()
                        },
                        new AttributeType
                        {
                            Name = CognitoConstants.Attributes.PERMISSIONS,
                            Value = string.Join(',', x.Permissions)
                        }
                    },
                    Username = x.Email,
                    UserPoolId = tenantId,
                    TemporaryPassword = temporaryPassword,
                    MessageAction = !string.IsNullOrWhiteSpace(temporaryPassword) ? MessageActionType.SUPPRESS : null
                });
                return Result.Success();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error creating user");
                return Result.Failure<AdminCreateUserResponse>(new Error("IdentityService.CreateUsersAsync", $"Error creating user {x.Email}"));
            }
        }).ToList();

        var results = await Task.WhenAll(tasks);

        var successCount = results.Count(x => x.IsSuccess);
        var failureExpression = results.Where(x => x.IsFailure);
        var errors = failureExpression.Any() ? string.Join("\r\n", failureExpression.Select(x => x.Error.Message)) : null;

        if (successCount > 0)
            return Result.Success((successCount, errors));

        return Result.Failure<(int createdQty, string? errors)>(new Error("IdentityService.CreateUsersAsync", errors!));
    }

    private async Task<Result<(string clientId, string clientSecret)>> GetClientCredentials(string userPoolId, CancellationToken cancellationToken)
    {
        var clientIdTask = settingsService.GetConfiguration(Application.Common.Models.Settings.CognitoClientId, userPoolId, cancellationToken);
        var clientsecretTask = settingsService.GetConfiguration(Application.Common.Models.Settings.CognitoSecretHash, userPoolId, cancellationToken);

        await Task.WhenAll(clientIdTask, clientsecretTask);

        if (clientIdTask.Result.IsFailure || string.IsNullOrWhiteSpace(clientIdTask.Result.Value))
            return Result.Failure<(string, string)>(IdentityErrors.Cognito.CLIENT_ID_EMPTY);

        if (clientsecretTask.Result.IsFailure || string.IsNullOrWhiteSpace(clientsecretTask.Result.Value))
            return Result.Failure<(string, string)>(IdentityErrors.Cognito.CLIENT_SECRET_EMPTY);

        return Result.Success((clientIdTask.Result.Value, clientsecretTask.Result.Value));
    }

    public async Task<Result<string>> GetTenantIdByNameAndCountry(string name, string country, CancellationToken cancellationToken)
    {
        ListUserPoolsResponse? userPools = null;
        do
        {
            try
            {
                userPools = await amazonCognitoIdentityProvider.ListUserPoolsAsync(new()
                {
                    MaxResults = 30,
                    NextToken = userPools?.NextToken ?? null
                }, cancellationToken);
                var tenantName = $"{name}-{country}";
                var userPool = userPools.UserPools.FirstOrDefault(x => x.Name == tenantName);

                if (userPool is not null)
                    return userPool.Id;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error trying to obtain pools");
                userPools = null;
            }
        }
        while (userPools != null && !string.IsNullOrWhiteSpace(userPools.NextToken));

        return Result.Failure<string>(IdentityErrors.Cognito.USER_POOL_NOT_FOUND);
    }

    private string CreateSecretHash(string username, string userPoolClientId, string userPoolClientSecret)
    {
        var clientIdBytes = Encoding.ASCII.GetBytes(username + userPoolClientId);
        var clientSecretBytes = Encoding.ASCII.GetBytes(userPoolClientSecret);
        var base64Str = "";
        using (var hmacsha256 = new HMACSHA256(clientSecretBytes))
        {
            var hash = hmacsha256.ComputeHash(clientIdBytes);
            base64Str = Convert.ToBase64String(hash);
        }
        return base64Str;
    }

    public async Task<Result> DeleteUserAsync(string email, CancellationToken cancellationToken)
    {
        var tenantId = tenantProvider.GetTenantId();

        try
        {
            await amazonCognitoIdentityProvider.AdminDeleteUserAsync(new()
            {
                Username = email,
                UserPoolId = tenantProvider.GetTenantId()
            }, cancellationToken);

            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error deleting user {email}");
            return Result.Failure(IdentityErrors.Cognito.DELETE_FAIL);
        }
    }

    public async Task<Result> RevokeTokenAsync(string tenantId, string token, CancellationToken cancellationToken)
    {
        try
        {
            var clientCredentialsResult = await GetClientCredentials(tenantId, cancellationToken);

            if (clientCredentialsResult.IsFailure)
            {
                logger.LogError("Error obtaining credentials from settings");
                return Result.Failure<LoginResponse>(IdentityErrors.Cognito.REVOKE_FAIL);
            }

            (var clientId, var clientSecret) = clientCredentialsResult.Value;

            var secretHash = CreateSecretHash(string.Empty, clientId, clientSecret);

            await amazonCognitoIdentityProvider.RevokeTokenAsync(new()
            {
                ClientId = clientId,
                ClientSecret = clientSecret,
                Token = token
            }, cancellationToken);

            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error revoking token for user");
            return Result.Failure(IdentityErrors.Cognito.REVOKE_FAIL);
        }
    }

    public async Task<Result<bool>> UserExists(string tenantId, string email, CancellationToken cancellationToken)
    {
        try
        {
            var user = await amazonCognitoIdentityProvider.AdminGetUserAsync(new()
            {
                Username = email,
                UserPoolId = tenantId
            }, cancellationToken);

            return Result.Success(true);
        }
        catch (Exception ex)
        {
            if (ex is UserNotFoundException)
                return Result.Success(false);

            logger.LogError(ex, $"Error obtaining user {email}");
            return Result.Failure<bool>(IdentityErrors.Cognito.ERROR_OBTAINING_USER);
        }
    }

    public async Task<Result> ForceChangePassword(string tenantId, string email, string password, CancellationToken cancellationToken)
    {
        try
        {
            await amazonCognitoIdentityProvider.AdminSetUserPasswordAsync(new()
            {
                Password = password,
                Permanent = true,
                Username = email,
                UserPoolId = tenantId
            }, cancellationToken);
            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error changing user {email} password forced");
            return Result.Failure(IdentityErrors.Cognito.FORCE_PASSWORD_CHANGE_FAIL);
        }
    }
    public async Task<Result<LoginResponse>> RefreshTokenAsync(string tenantId, string email, string refreshToken, CancellationToken cancellationToken)
    {
        try
        {
            var clientCredentialsResult = await GetClientCredentials(tenantId, cancellationToken);

            if (clientCredentialsResult.IsFailure)
            {
                logger.LogError("Error obtaining credentials from settings");
                return Result.Failure<LoginResponse>(IdentityErrors.Cognito.REFRESH_GENERIC);
            }

            (var clientId, var clientSecret) = clientCredentialsResult.Value;
            var secretHash = CreateSecretHash(email, clientId, clientSecret);

            var authParameters = new Dictionary<string, string>
            {
                {"REFRESH_TOKEN", refreshToken },
                {"SECRET_HASH", secretHash }
            };
        
            var response = await amazonCognitoIdentityProvider.AdminInitiateAuthAsync(new AdminInitiateAuthRequest
            {
                AuthFlow = AuthFlowType.REFRESH_TOKEN_AUTH,
                ClientId = clientId,
                UserPoolId = tenantId,
                AuthParameters = authParameters
            });

            return Result.Success(new LoginResponse(response.ChallengeName, response.Session, tenantId, response.AuthenticationResult.AccessToken, response.AuthenticationResult.RefreshToken));
        }
        catch(Exception ex)
        {
            logger.LogError(ex, "Error refreshing token");
            return Result.Failure<LoginResponse>(IdentityErrors.Cognito.REFRESH_GENERIC);
        }
    }

    public async Task<Result> EditUserAsync(string email, UpdateUserRequest request, string tenantId, CancellationToken cancellationToken)
    {
        List<AttributeType> attributesToUpdate = new();

        if ((request.Permissions?.Count ?? 0) != 0)
        {
            attributesToUpdate.Add(new()
            {
                Name = CognitoConstants.Attributes.PERMISSIONS,
                Value = string.Join(',', request.Permissions!)
            });
        }

        if (!string.IsNullOrWhiteSpace(request.NewEmail))
        {
            attributesToUpdate.Add(new()
            {
                Name = "email",
                Value = request.NewEmail
            });
        }

        if (request.Role != null)
        {
            attributesToUpdate.Add(new()
            {
                Name = CognitoConstants.Attributes.ROLE,
                Value = request.Role.ToString()
            });
        }

        if (attributesToUpdate.Count == 0)
            return Result.Failure(IdentityErrors.Cognito.UPDATE_USER_NOTHING_TO_UPDATE);

        try
        {
            await amazonCognitoIdentityProvider.AdminUpdateUserAttributesAsync(new()
            {
                Username = email,
                UserPoolId = tenantId,
                UserAttributes = attributesToUpdate
            }, cancellationToken);

            return Result.Success();
        }
        catch(Exception ex)
        {
            logger.LogError(ex, "Error updating user.");
            return Result.Failure(IdentityErrors.Cognito.UPDATE_USER_FAIL);
        }
    }

    public async Task<Result> ForgotPasswordAsync(string tenantId, string email, CancellationToken cancellationToken)
    {
        try
        {
            var clientCredentialsResult = await GetClientCredentials(tenantId, cancellationToken);

            if (clientCredentialsResult.IsFailure)
            {
                logger.LogError($"Error obtaining credentials from settings ({clientCredentialsResult.Error.Code})");
                return Result.Failure<LoginResponse>(IdentityErrors.Cognito.FORGOT_GENERIC);
            }
        
            (var clientId, var clientSecret) = clientCredentialsResult.Value;

            var secretHash = CreateSecretHash(email, clientId, clientSecret);

            var result = await amazonCognitoIdentityProvider.ForgotPasswordAsync(new ForgotPasswordRequest
            {
                ClientId = clientId,
                SecretHash = secretHash,
                Username = email
            });

            return Result.Success();
        }
        catch(Exception ex)
        {
            logger.LogError(ex, "Error attempting to send forgot password email");
            return Result.Failure(IdentityErrors.Cognito.FORGOT_GENERIC);
        }
    }

    public async Task<Result> ConfirmForgotPasswordAsync(string tenantId, string email, string password, string code, CancellationToken cancellationToken)
    {
        try
        {
            var clientCredentialsResult = await GetClientCredentials(tenantId, cancellationToken);

            if (clientCredentialsResult.IsFailure)
            {
                logger.LogError($"Error obtaining credentials from settings ({clientCredentialsResult.Error.Code})");
                return Result.Failure<LoginResponse>(IdentityErrors.Cognito.CONFIRM_FORGOT_GENERIC);
            }
            
            (var clientId, var clientSecret) = clientCredentialsResult.Value;

            var secretHash = CreateSecretHash(email, clientId, clientSecret);

            await amazonCognitoIdentityProvider.ConfirmForgotPasswordAsync(new()
            {
                ClientId = clientId,
                SecretHash = secretHash,
                Username = email,
                ConfirmationCode = code,
                Password = password
            });

            return Result.Success();
        }
        catch(Exception ex)
        {

            var error = true switch
            {
                true when ex is ExpiredCodeException => IdentityErrors.Cognito.CONFIRM_FORGOT_EXPIRED_CODE,
                true when ex is CodeMismatchException => IdentityErrors.Cognito.CONFIRM_FORGOT_CODE_MISMATCH,
                true when ex is InvalidPasswordException => IdentityErrors.Cognito.INVALID_PASS_FORMAT,
                true when ex is PasswordHistoryPolicyViolationException => IdentityErrors.Cognito.CONFIRM_FORGOT_PASSWORD_HISTORY,
                _ => IdentityErrors.Cognito.CONFIRM_FORGOT_GENERIC
            };

            if(error == IdentityErrors.Cognito.CONFIRM_FORGOT_GENERIC)
                logger.LogError(ex, "Error attempting to confirm forgot password code");

            return Result.Failure(error);
        }
    }

}
