﻿using System.Security.Cryptography;
using UsersManagement.Application.Common.Interfaces;

namespace UsersManagement.Infrastructure.Identity;
internal class CognitoPasswordGenerator : IPasswordGenerator
{
    private const string UppercaseChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private const string LowercaseChars = "abcdefghijklmnopqrstuvwxyz";
    private const string Numbers = "0123456789";
    private const string SpecialChars = "^$*.[]{}()?!\"@#%&/\\,><':;|_~`=+-";
    private const ushort MAX_CHARACTERS = 16;

    private static char GetRandom<PERSON>haracter(string characters)
    {
        int index = RandomNumberGenerator.GetInt32(characters.Length);
        return characters[index];
    }

    private static void RandomizeCharacters(char[] array)
    {
        for (int i = array.Length - 1; i > 0; i--)
        {
            int j = RandomNumberGenerator.GetInt32(i + 1);
            char temp = array[i];
            array[i] = array[j];
            array[j] = temp;
        }
    }

    public string Generate()
    {
        char[] password = new char[MAX_CHARACTERS];

        password[0] = GetRandomCharacter(UppercaseChars);
        password[1] = GetRandomCharacter(LowercaseChars);
        password[2] = GetRandomCharacter(Numbers);
        password[3] = GetRandomCharacter(SpecialChars);

        string allCharacters = UppercaseChars + LowercaseChars + Numbers + SpecialChars;
        
        for (int i = 4; i < MAX_CHARACTERS; i++)
        {
            password[i] = GetRandomCharacter(allCharacters);
        }

        RandomizeCharacters(password);

        return new string(password);
    }
}
