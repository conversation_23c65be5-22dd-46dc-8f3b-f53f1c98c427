﻿namespace UsersManagement.Infrastructure.Identity;

internal static class CognitoChallengeValidator
{
    public const string USERNAME = "USERNAME";
    public const string SMS_MFA_CODE = "SMS_MFA_CODE";
    public const string EMAIL_OTP_CODE = "EMAIL_OTP_CODE";
    public const string PASSWORD_CLAIM_SIGNATURE = "PASSWORD_CLAIM_SIGNATURE";
    public const string PASSWORD_CLAIM_SECRET_BLOCK = "PASSWORD_CLAIM_SECRET_BLOCK";
    public const string TIMESTAMP = "TIMESTAMP";
    public const string ANSWER = "ANSWER";
    public const string NEW_PASSWORD = "NEW_PASSWORD";
    public const string SOFTWARE_TOKEN_MFA_CODE = "SOFTWARE_TOKEN_MFA_CODE";
    public const string DEVICE_KEY = "DEVICE_KEY";
    public const string SRP_A = "SRP_A";
    public const string SESSION = "SESSION";

    private static readonly HashSet<string> SMS_MFA_ValidKeys = new()
    {
        USERNAME,
        SMS_MFA_CODE
    };

    private static readonly HashSet<string> EMAIL_OTP_ValidKeys = new()
    {
        USERNAME,
        EMAIL_OTP_CODE
    };

    private static readonly HashSet<string> PASSWORD_VERIFIER_ValidKeys = new()
    {
        USERNAME,
        PASSWORD_CLAIM_SIGNATURE,
        PASSWORD_CLAIM_SECRET_BLOCK,
        TIMESTAMP
    };

    private static readonly HashSet<string> CUSTOM_CHALLENGE_ValidKeys = new()
    {
        USERNAME,
        ANSWER
    };

    private static readonly HashSet<string> NEW_PASSWORD_REQUIRED_ValidKeys = new()
    {
        USERNAME,
        NEW_PASSWORD
    };

    private static readonly HashSet<string> SOFTWARE_TOKEN_MFA_ValidKeys = new()
    {
        USERNAME,
        SOFTWARE_TOKEN_MFA_CODE
    };

    private static readonly HashSet<string> DEVICE_SRP_AUTH_ValidKeys = new()
    {
        USERNAME,
        DEVICE_KEY,
        SRP_A
    };

    private static readonly HashSet<string> DEVICE_PASSWORD_VERIFIER_ValidKeys = new()
    {
        USERNAME,
        DEVICE_KEY,
        PASSWORD_CLAIM_SIGNATURE,
        PASSWORD_CLAIM_SECRET_BLOCK,
        TIMESTAMP
    };

    private static readonly HashSet<string> MFA_SETUP_ValidKeys = new()
    {
        USERNAME,
        SESSION
    };

    private static readonly HashSet<string> SELECT_MFA_TYPE_ValidKeys = new()
    {
        USERNAME,
        ANSWER
    };

    public static Dictionary<string, string> ValidateAndFilterKeys(string challengeName, Dictionary<string, string> input)
    {
        var field = typeof(CognitoChallengeValidator)
            .GetField($"{challengeName}_ValidKeys", 
                System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.NonPublic);

        if (field is null)
            return [];

        var validKeys = (HashSet<string>)field.GetValue(null)!;

        var validData = input
            .Where(kvp => validKeys.Contains(kvp.Key))
            .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

        return validData;
    }
}
