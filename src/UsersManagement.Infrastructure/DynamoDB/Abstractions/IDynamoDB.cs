﻿namespace UsersManagement.Infrastructure.DynamoDB.Abstractions;

public interface IDynamoDB
{
    Task<Result<string>> GetValueAsync(string table, string keyField, string field, string key, CancellationToken cancellationToken = default);
    Task<Result<Dictionary<string, string>>> GetValuesAsync(string table, string keyField, string key, CancellationToken cancellationToken = default);
    Task<Result<List<Dictionary<string, string>>>> GetMultipleItems(string table, string queryField, string key, int pageSize = 10, string? nextToken = null, CancellationToken cancellationToken = default);
    Task<Result<List<Dictionary<string, string>>>> GetMultipleItems(string table, Dictionary<string, string> keyFilter, Dictionary<string, object> filters, int pageSize = 10, string? nextToken = null, CancellationToken cancellationToken = default);
    Task<Result> UpdateValueAsync(string table, string keyField, string field, string key, string newValue, CancellationToken cancellationToken = default);
    Task<Result> AddBulkAsync(string table, Dictionary<string, string> keyFilter, Dictionary<string, object> filters, string field, int addQty = 1, CancellationToken cancellationToken = default);
    Task<Result> InsertItemAsync(string table, Dictionary<string, object> item, CancellationToken cancellationToken = default);
}
