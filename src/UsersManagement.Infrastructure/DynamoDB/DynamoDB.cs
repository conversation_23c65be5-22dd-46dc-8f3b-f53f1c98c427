﻿using System.Collections;
using System.Text;
using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DocumentModel;
using Amazon.DynamoDBv2.Model;
using Microsoft.Extensions.Logging;
using N5.Result;
using UsersManagement.Infrastructure.DynamoDB.Abstractions;

namespace UsersManagement.Infrastructure.DynamoDB;

internal class DynamoDB(IAmazonDynamoDB dynamodb, ILogger<DynamoDB> logger) : IDynamoDB
{
    public async Task<Result<string>> GetValueAsync(string table, string keyField, string field, string key, CancellationToken cancellationToken = default)
    {
        try
        {
            var item = await dynamodb.GetItemAsync(new()
            {
                Key = new() { { keyField, new(key) } },
                TableName = table
            });
            if (item.HttpStatusCode != System.Net.HttpStatusCode.OK)
                return Result.Failure<string>(new("DynamoDB.GetValueAsync", "Error while trying to retrieve value"));

            return Result.Success(item.Item[field].S);
        }
        catch (KeyNotFoundException ex)
        {
            logger.LogError(ex, "DynamoDB: Trying to retrieve non-existent value");
            return Result.Failure<string>(new("DynamoDB.GetValueAsync", "Error while trying to retrieve value"));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "DynamoDB: Error while trying to retrieve value");
            return Result.Failure<string>(new("DynamoDB.GetValueAsync", "Error while trying to retrieve value"));
        }
    }

    public async Task<Result<Dictionary<string, string>>> GetValuesAsync(string table, string keyField, string key, CancellationToken cancellationToken = default)
    {
        try
        {
            var item = await dynamodb.GetItemAsync(new()
            {
                Key = new() { { keyField, new() { S = key } } },
                TableName = table
            });
            if (item.HttpStatusCode != System.Net.HttpStatusCode.OK)
                return Result.Failure<Dictionary<string, string>>(new("DynamoDB.GetValueAsync", "Error while trying to retrieve value"));
            
            return Result.Success(item.Item.ToDictionary(x => x.Key, x => x.Value.S));
        }
        catch (KeyNotFoundException ex)
        {
            logger.LogError(ex, "DynamoDB: Trying to retrieve non-existent value");
            return Result.Failure<Dictionary<string, string>>(new("DynamoDB.GetValueAsync", "Error while trying to retrieve value"));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "DynamoDB: Error while trying to retrieve value");
            return Result.Failure<Dictionary<string, string>>(new("DynamoDB.GetValueAsync", "Error while trying to retrieve value"));
        }
    }

    public async Task<Result<List<Dictionary<string, string>>>> GetMultipleItems(string table, string queryField, string key, int pageSize = 10, string? nextToken = null, CancellationToken cancellationToken = default)
        => await GetMultipleItems(table, new() { { queryField, key } }, new Dictionary<string, object>(), pageSize, nextToken, cancellationToken);

    public async Task<Result<List<Dictionary<string, string>>>> GetMultipleItems(string table, Dictionary<string, string> keyFilter, Dictionary<string, object> filters, int pageSize = 10, string? nextToken = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var (keyConditionExpression, filterExpression, expressionAttributeValues, expressionAttributeNames) = GetFilterQuery(keyFilter, filters);

            var queryRequest = new QueryRequest
            {
                TableName = table,
                KeyConditionExpression = keyConditionExpression,
                FilterExpression = string.IsNullOrWhiteSpace(filterExpression) ? null : filterExpression,
                ExpressionAttributeValues = expressionAttributeValues,
                ExpressionAttributeNames = expressionAttributeNames,
                Limit = pageSize,
                ExclusiveStartKey = DecodeToken(nextToken)
            };

            var response = await dynamodb.QueryAsync(queryRequest, cancellationToken);

            return response.Items.Select(x => x.Select(z =>
            {
                var value = z.Value switch
                {
                    { S: var s } when !string.IsNullOrWhiteSpace(s) => s,
                    { N: var n } when !string.IsNullOrWhiteSpace(n) => n,
                    { BOOL: var b } when b => b.ToString(),
                    _ => string.Empty
                };
                return new KeyValuePair<string, string>(z.Key, value);
            }).ToDictionary()).ToList();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "DynamoDB: Error while retrieving values");
            return Result.Failure<List<Dictionary<string, string>>>(new("DynamoDB.GetMultipleItems", "Error while retrieving values"));
        }
    }

    public async Task<Result> UpdateValueAsync(string table, string keyField, string field, string key, string newValue, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await dynamodb.UpdateItemAsync(new()
            {
                TableName = table,
                Key = new() { { keyField, new() { S = key } } },
                AttributeUpdates = new()
                {
                    { field, new () { Action = AttributeAction.PUT, Value = new(newValue) } }
                }
            });

            if (result.HttpStatusCode != System.Net.HttpStatusCode.OK)
                return Result.Failure(new Error("DynamoDB.UpdateValueAsync", "Error trying to update dynamodb value"));

            return Result.Success();
        }
        catch(Exception ex)
        {
            logger.LogError(ex, "DynamoDB: Error trying to update value");
            return Result.Failure(new Error("DynamoDB.UpdateValueAsync", "Error trying to update dynamodb value"));
        }
    }

    public async Task<Result> AddBulkAsync(string table, Dictionary<string, string> keyFilter, Dictionary<string, object> filters, string field, int addQty = 1, CancellationToken cancellationToken = default)
    {
        try
        {
            var (keyConditionExpression, filterExpression, expressionAttributeValues, expressionAttributeNames) = GetFilterQuery(keyFilter, filters);
            
            var keys = new Dictionary<string, AttributeValue>();
            var conditions = keyConditionExpression.Split("AND", StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
            
            foreach(var condition in conditions)
            {
                var splitted = condition.Split('=');
                var key = splitted[0].Trim();
                var value = splitted[1].Trim();

                if(expressionAttributeNames.TryGetValue(key, out var keyWithoutName))
                {
                    expressionAttributeNames.Remove(key);
                    key = keyWithoutName;
                }

                var keyValue = expressionAttributeValues[value];
                keys.Add(key, keyValue);
                expressionAttributeValues.Remove(value);
            }

            expressionAttributeValues.Add($":{field}", new()
            {
                N = $"{addQty}"
            });

            var request = new UpdateItemRequest
            {
                Key = keys,
                TableName = table,
                UpdateExpression = $"ADD {field} :{field}",
                ConditionExpression = string.IsNullOrWhiteSpace(filterExpression) ? null : filterExpression,
                ExpressionAttributeValues = expressionAttributeValues,
                ExpressionAttributeNames = expressionAttributeNames
            };

            var response = await dynamodb.UpdateItemAsync(request, cancellationToken);

            return Result.Success();
        }
        catch(Exception ex)
        {
            logger.LogError(ex, "DynamoDB: Error trying to update values");
            return Result.Failure(new Error("DynamoDB.UpdateBulkAsync", "Multiple update error"));
        }
    }

    private (string keyConditionExpression, string filterExpression, Dictionary<string, AttributeValue> expressionAttributeValues, Dictionary<string, string> expressionAttributesNames) 
        GetFilterQuery(Dictionary<string, string> keyFilters, Dictionary<string, object> filters)
    {
        var filterExpression = "";
        var keyConditionExpression = "";
        var expressionAttributeValues = new Dictionary<string, AttributeValue>();
        var expressionAttributeNames = new Dictionary<string, string>();
        bool firstCondition = true;

        static (string filterExpression, Dictionary<string, AttributeValue> expressionAttributeValues, KeyValuePair<string, string>? expressionAttributeNames) GetFilter(KeyValuePair<string, object> filter)
        {
            var isEnumerable = filter.Value is IEnumerable<string>;
            var key = filter.Key;
            var filterKey = filter.Key;
            var filterExpression = "";
            KeyValuePair<string, string>? expressionAttributeNames = null;
            var expressionAttributeValues = new Dictionary<string, AttributeValue>();

            if (key.Equals("value", StringComparison.CurrentCultureIgnoreCase))
            {
                key = "#val";
                expressionAttributeNames = new("#val", "value");
            }

            switch (true)
            {
                case true when filter.Value is IEnumerable<string> strings:
                    var keys = "";
                    foreach (var str in strings)
                    {
                        var paramKey = $":{str}_param";
                        keys += paramKey;
                        expressionAttributeValues.Add(paramKey, new(str));
                    }
                    filterExpression += $"{key} IN ({keys})";

                    break;
                default:
                    filterExpression += $"{key} = :{filterKey}";
                    expressionAttributeValues.Add($":{filterKey}", new(filter.Value.ToString()));
                    break;
            }
            return (filterExpression, expressionAttributeValues, expressionAttributeNames);
        }

        foreach (var item in keyFilters)
        {
            if (!firstCondition)
                keyConditionExpression += " AND ";

            firstCondition = false;

            var filter = GetFilter(new KeyValuePair<string, object>(item.Key, item.Value));

            keyConditionExpression += filter.filterExpression;
            
            if (filter.expressionAttributeNames.HasValue)
                expressionAttributeNames.Add(filter.expressionAttributeNames.Value.Key, filter.expressionAttributeNames.Value.Value);

            expressionAttributeValues = expressionAttributeValues.Concat(filter.expressionAttributeValues).ToDictionary();
        }

        firstCondition = true;

        foreach (var item in filters)
        {
            if (!firstCondition)
                filterExpression += " AND ";

            firstCondition = false;

            var filter = GetFilter(new KeyValuePair<string, object>(item.Key, item.Value));

            filterExpression += filter.filterExpression;

            if (filter.expressionAttributeNames.HasValue)
                expressionAttributeNames.Add(filter.expressionAttributeNames.Value.Key, filter.expressionAttributeNames.Value.Value);

            expressionAttributeValues = expressionAttributeValues.Concat(filter.expressionAttributeValues).ToDictionary();
        }

        return (keyConditionExpression, filterExpression, expressionAttributeValues, expressionAttributeNames);
    }

    // Decode the token using DynamoDB DocumentModel to ensure proper reconstruction.
    private static Dictionary<string, AttributeValue>? DecodeToken(string? token)
    {
        if (string.IsNullOrEmpty(token))
            return null;

        try
        {
            var bytes = Convert.FromBase64String(token);
            var json = Encoding.UTF8.GetString(bytes);
            var document = Document.FromJson(json);
            return document.ToAttributeMap();
        }
        catch (Exception)
        {
            // Log the error if needed.
            return null;
        }
    }
    
    public async Task<Result> InsertItemAsync(string table, Dictionary<string, object> item, CancellationToken cancellationToken = default)
    {
        try
        {
            var attributeValues = new Dictionary<string, AttributeValue>();
            
            foreach (var kvp in item)
            {
                // Convert each item value to the appropriate AttributeValue type
                if (kvp.Value is string stringValue)
                {
                    attributeValues.Add(kvp.Key, new AttributeValue { S = stringValue });
                }
                else if (kvp.Value is int intValue || kvp.Value is long longValue)
                {
                    var numericValue = kvp.Value.ToString();
                    attributeValues.Add(kvp.Key, new AttributeValue { N = numericValue });
                }
                else if (kvp.Value is bool boolValue)
                {
                    attributeValues.Add(kvp.Key, new AttributeValue { BOOL = boolValue });
                }
                else
                {
                    // Default to string representation for other types
                    attributeValues.Add(kvp.Key, new AttributeValue { S = kvp.Value?.ToString() ?? string.Empty });
                }
            }
            
            var request = new PutItemRequest
            {
                TableName = table,
                Item = attributeValues
            };
            
            var response = await dynamodb.PutItemAsync(request, cancellationToken);
            
            if (response.HttpStatusCode != System.Net.HttpStatusCode.OK)
            {
                logger.LogError("DynamoDB: Error inserting item into table {Table}", table);
                return Result.Failure(new Error("DynamoDB.InsertItemAsync", "Error inserting item into DynamoDB"));
            }
            
            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "DynamoDB: Error inserting item into table {Table}", table);
            return Result.Failure(new Error("DynamoDB.InsertItemAsync", "Error inserting item into DynamoDB"));
        }
    }
}
