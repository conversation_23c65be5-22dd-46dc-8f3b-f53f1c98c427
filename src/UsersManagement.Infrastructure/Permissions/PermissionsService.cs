﻿using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Domain.Entities;
using UsersManagement.Infrastructure.DynamoDB.Abstractions;

namespace UsersManagement.Infrastructure.Permissions;

internal class PermissionsService(IDynamoDB dynamoDb) : IPermissionsService
{
    private const string TABLENAME = "permissions";
    private const string TABLEKEY = "tenant_id";

    public async Task<Result<bool>> ExistsPermissions(string tenantId, List<string> permissions, CancellationToken cancellationToken)
    {
        var tasks = permissions.Select(async permission =>
        {
            return await dynamoDb.GetMultipleItems(TABLENAME, new()
            {
                { TABLEKEY, tenantId },
                { "value", permission }
            }, new Dictionary<string, object>(), cancellationToken: cancellationToken);
        });

        var results = await Task.WhenAll(tasks);

        return results.All(x => x.IsSuccess) ? Result.Success(results.All(x => x.Value.Count == 1) && results.Length == permissions.Count) : Result.Failure<bool>(results.First(x => x.IsFailure).Error);
    }

    public async Task<Result<List<Permission>>> GetAllPermissions(string tenantId, CancellationToken cancellationToken)
    {
        var permissionsResult = await dynamoDb.GetMultipleItems(TABLENAME, TABLEKEY, tenantId, cancellationToken: cancellationToken);

        if (permissionsResult.IsFailure)
            return Result.Failure<List<Permission>>(permissionsResult.Error);

        var permissions = permissionsResult.Value.Select(x => new Permission
        {
            Color = x["color"],
            TenantId = tenantId,
            Title_En = x["title_en"],
            Title_Es = x["title_es"],
            Title_Pt = x["title_pt"],
            UsersCount = int.Parse(x["users_count"]),
            Value = x["value"],
        }).ToList();

        return Result.Success(permissions);
    }

    public async Task<Result> AddPermissionUserCount(string tenantId, List<string> permissions, int userCount, CancellationToken cancellationToken)
    {
        var tasks = permissions.Select(async permission =>
        {
            return await dynamoDb.AddBulkAsync(TABLENAME, new Dictionary<string, string>()
            {
                { TABLEKEY, tenantId },
                { "value", permission }
            }, new(), "users_count", userCount, cancellationToken: cancellationToken);
        });

        var results = await Task.WhenAll(tasks);

        return results.All(x => x.IsSuccess) ? Result.Success() : Result.Failure(results.First(x => x.IsFailure).Error);
    }
}
