﻿using Refit;
using UsersManagement.Domain.DTO;

namespace UsersManagement.Infrastructure.Licenses;
public interface ILicenseApi
{
    [Headers("Content-Type: application/json;charset=utf-8")]
    [Post("/api/v1/licenses/consume")]
    Task<IApiResponse> Consume([HeaderCollection] IDictionary<string, string> headers, CancellationToken cancellationToken = default);

    [Headers("Content-Type: application/json;charset=utf-8")]
    [Post("/api/v1/licenses/refund")]
    Task<IApiResponse> Refund([HeaderCollection] IDictionary<string, string> headers, CancellationToken cancellationToken = default);

    [Headers("Content-Type: application/json;charset=utf-8")]
    [Get("/api/v1/licenses")]
    Task<License> Get([HeaderCollection] IDictionary<string, string> headers, CancellationToken cancellationToken = default);
}
