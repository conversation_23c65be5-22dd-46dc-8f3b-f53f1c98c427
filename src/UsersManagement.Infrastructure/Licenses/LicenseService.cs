﻿using Microsoft.Extensions.Logging;
using N5.Result;
using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Domain.DTO;

namespace UsersManagement.Infrastructure.Licenses;

internal class LicenseService(ILicenseApi licenseApi, ILogger<LicenseService> logger) : ILicenseService
{
    public async Task<Result> Consume(string tenantId, CancellationToken cancellationToken)
    {
        try
        {
            var headers = new Dictionary<string, string> { { "X-Tenant-Id", tenantId } };
            var response = await licenseApi.Consume(headers, cancellationToken);

            if (response.IsSuccessStatusCode)
                return Result.Success();
            else
                return Result.Failure(new Error("LICENSE_CONSUME_FAILED", "License consume failed"));
        }
        catch(Exception ex)
        {
            logger.LogError(ex, "Error consuming license");
            return Result.Failure(new Error("LICENSE_CONSUME_FAILED", "License consume failed"));
        }
    }

    public async Task<Result> Refund(string tenantId, CancellationToken cancellationToken)
    {
        try
        {
            var headers = new Dictionary<string, string> { { "X-Tenant-Id", tenantId } };
            var response = await licenseApi.Refund(headers, cancellationToken);

            if (response.IsSuccessStatusCode)
                return Result.Success();
            else
                return Result.Failure(new Error("LICENSE_REFUND_FAILED", "License refund failed"));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error refunding license");
            return Result.Failure(new Error("LICENSE_REFUND_FAILED", "License refund failed"));
        }
    }

    public async Task<Result<License>> GetLicenseAsync(string tenantId, CancellationToken cancellationToken)
    {
        try
        {
            var headers = new Dictionary<string, string> { { "X-Tenant-Id", tenantId } };
            var response = await licenseApi.Get(headers, cancellationToken);
            return Result.Success(response);
        }
        catch(Exception ex)
        {
            logger.LogError(ex, "Error retrieving license");
            return Result.Failure<License>(new Error("LICENCE_GET_FAILED", "Retrieving license fail."));
        }
    }
}
