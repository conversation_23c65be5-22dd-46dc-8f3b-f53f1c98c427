using Microsoft.Extensions.Logging;
using System.Text;
using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Infrastructure.DynamoDB.Abstractions;

namespace UsersManagement.Infrastructure.Sessions;

/// <summary>
/// Implementation of ISessionService that manages user sessions using DynamoDB
/// </summary>
public class SessionService : ISessionService
{
    private const string TableName = "TempStore";
    private const string TenantIdField = "tenant_id";
    private const string KeyField = "id";
    private const string JwtField = "jwt";
    private const string TtlField = "ttl";
    private const int TtlMinutes = 1;

    private readonly IDynamoDB _dynamoDB;
    private readonly ILogger<SessionService> _logger;

    public SessionService(IDynamoDB dynamoDB, ILogger<SessionService> logger)
    {
        _dynamoDB = dynamoDB;
        _logger = logger;
    }

    /// <inheritdoc />
    public async Task<Result<string>> StoreJwtAsync(string tenantId, string jwt, CancellationToken cancellationToken = default)
    {
        try
        {
            // Generate a GUID key
            string key = Guid.NewGuid().ToString();
            
            // Calculate TTL (current time + 1 minute)
            var ttl = DateTimeOffset.UtcNow.AddMinutes(TtlMinutes).ToUnixTimeSeconds();
            
            // Create item with all values to store in DynamoDB
            var item = new Dictionary<string, object>
            {
                { TenantIdField, tenantId },
                { KeyField, key },
                { JwtField, jwt },
                { TtlField, ttl.ToString() }
            };
            
            // Store the item in DynamoDB
            var result = await _dynamoDB.InsertItemAsync(TableName, item, cancellationToken);
            
            if (result.IsFailure)
            {
                _logger.LogError("Failed to store JWT in DynamoDB: {Error}", result.Error.Message);
                return Result.Failure<string>(result.Error);
            }
            
            // Convert the key to base64
            string base64Key = Convert.ToBase64String(Encoding.UTF8.GetBytes(key));
            
            return Result.Success(base64Key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error trying to store JWT");
            return Result.Failure<string>(new Error("JWT_STORAGE_FAILED", "Error trying to store JWT"));
        }
    }

    /// <inheritdoc />
    public async Task<Result<string>> GetJwtAsync(string tenantId, string base64Key, CancellationToken cancellationToken = default)
    {
        try
        {
            // Decode the base64 key
            byte[] keyBytes = Convert.FromBase64String(base64Key);
            string key = Encoding.UTF8.GetString(keyBytes);
            
            // Get the JWT from DynamoDB
            var result = await _dynamoDB.GetMultipleItems(TableName, new Dictionary<string, string>() { { TenantIdField, tenantId}, { KeyField, key} }, new(), pageSize: 1, cancellationToken: cancellationToken);
            
            if (result.IsFailure)
            {
                _logger.LogError("Failed to retrieve JWT from DynamoDB: {Error}", result.Error.Message);
                return Result.Failure<string>(new Error("JWT_RETRIEVAL_FAILED", "Failed to retrieve JWT"));
            }

            var jwt = result.Value.First();

            if (long.Parse(jwt[TtlField]) < DateTimeOffset.UtcNow.ToUnixTimeSeconds())
                return Result.Failure<string>(new Error("EXPIRED_SESSION", "This session is already expired, please retrieve a new one."));

            return jwt[JwtField];
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error trying to retrieve JWT");
            return Result.Failure<string>(new Error("JWT_RETRIEVAL_FAILED", "Error trying to retrieve JWT"));
        }
    }
}
