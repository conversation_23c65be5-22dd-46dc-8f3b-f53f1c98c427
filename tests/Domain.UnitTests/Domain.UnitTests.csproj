﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <RootNamespace>UsersManagement.Domain.UnitTests</RootNamespace>
        <AssemblyName>UsersManagement.Domain.UnitTests</AssemblyName>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.NET.Test.Sdk" />
        <PackageReference Include="nunit" />
        <PackageReference Include="NUnit.Analyzers">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="NUnit3TestAdapter" />
        <PackageReference Include="coverlet.collector">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="FluentAssertions" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\src\UsersManagement.Domain\UsersManagement.Domain.csproj" />
    </ItemGroup>

</Project>
