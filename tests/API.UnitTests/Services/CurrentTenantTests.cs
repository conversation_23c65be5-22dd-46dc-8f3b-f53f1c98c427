﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using UsersManagement.API.Services;

namespace API.UnitTests.Services;

public class CurrentTenantTests
{
    private TenantProvider CreateTenantProviderWithHeaders(IHeaderDictionary headers)
    {
        var context = new DefaultHttpContext();
        foreach (var header in headers)
        {
            context.Request.Headers[header.Key] = header.Value;
        }
        var httpContextAccessor = new HttpContextAccessor { HttpContext = context };
        return new TenantProvider(httpContextAccessor);
    }

    [Test]
    public void GetTenantId_ReturnsValue_WhenHeaderExists()
    {
        // Arrange
        var expectedTenantId = "tenant123";
        var headers = new HeaderDictionary
            {
                { TenantProvider.TenantIdHeader, new StringValues(expectedTenantId) }
            };

        var provider = CreateTenantProviderWithHeaders(headers);

        // Act
        var tenantId = provider.GetTenantId();

        expectedTenantId.Should().BeSameAs(tenantId);
    }

    [Test]
    public void GetTenantName_ReturnsValue_WhenHeaderExists()
    {
        // Arrange
        var expectedTenantName = "NombreDelTenant";
        var headers = new HeaderDictionary
            {
                { TenantProvider.TenantNameHeader, new StringValues(expectedTenantName) }
            };

        var provider = CreateTenantProviderWithHeaders(headers);

        // Act
        var tenantName = provider.GetTenantName();

        expectedTenantName.Should().BeSameAs(tenantName);
    }

    [Test]
    public void GetTenantCountry_ReturnsValue_WhenHeaderExists()
    {
        // Arrange
        var expectedTenantCountry = "PaísX";
        var headers = new HeaderDictionary
            {
                { TenantProvider.TenantCountryHeader, new StringValues(expectedTenantCountry) }
            };

        var provider = CreateTenantProviderWithHeaders(headers);

        // Act
        var tenantCountry = provider.GetTenantCountry();

        // Assert
        expectedTenantCountry.Should().BeSameAs(tenantCountry);
    }

    [Test]
    public void GetTenantId_ThrowsException_WhenHeaderMissing()
    {
        // Arrange
        var headers = new HeaderDictionary();
        var provider = CreateTenantProviderWithHeaders(headers);

        //Act & Assert
        FluentActions.Invoking(provider.GetTenantId)
            .Should()
            .Throw<ApplicationException>()
            .WithMessage("Header not present");
    }

    [Test]
    public void GetTenantCountry_ThrowsException_WhenHeaderMissing()
    {
        // Arrange
        var headers = new HeaderDictionary();
        var provider = CreateTenantProviderWithHeaders(headers);

        //Act & Assert
        FluentActions.Invoking(provider.GetTenantCountry)
            .Should()
            .Throw<ApplicationException>()
            .WithMessage("Header not present");
    }

    [Test]
    public void GetTenantName_ThrowsException_WhenHeaderMissing()
    {
        // Arrange
        var headers = new HeaderDictionary();
        var provider = CreateTenantProviderWithHeaders(headers);

        //Act & Assert
        FluentActions.Invoking(provider.GetTenantName)
            .Should()
            .Throw<ApplicationException>()
            .WithMessage("Header not present");
    }

    [Test]
    public void GetHeaderOrThrow_ThrowsException_WhenHeaderExistsButEmpty()
    {
        // Arrange
        var headers = new HeaderDictionary
        {
            { TenantProvider.TenantIdHeader, new StringValues((string?[])[]) }
        };

        var provider = CreateTenantProviderWithHeaders(headers);

        //Act & Assert
        FluentActions.Invoking(provider.GetTenantId)
            .Should()
            .Throw<ApplicationException>()
            .WithMessage("Header not present");
    }

    [Test]
    public void GetTenantId_ThrowsException_WhenHttpContextIsNull()
    {
        // Arrange
        var httpContextAccessor = new HttpContextAccessor { HttpContext = null };
        var provider = new TenantProvider(httpContextAccessor);

        // Act & Assert
        FluentActions.Invoking(provider.GetTenantId)
            .Should()
            .Throw<NotSupportedException>()
            .WithMessage("Wrong application context");
    }
}
