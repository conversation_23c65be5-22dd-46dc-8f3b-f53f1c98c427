﻿namespace UsersManagement.Application.FunctionalTests.Management.Queries;

using UsersManagement.Application.Management.Queries.GetLicenses;
using static Testing;

public class GetLicenseTests
{
    [Test]
    public async Task ShouldReturnLicenses()
    {
        var command = new GetLicenseQuery();

        var response = await SendAsync(command);

        response.Should().NotBeNull();
        response.IsSuccess.Should().BeTrue();

        response.Value.Total.Should().Be(10);
        response.Value.Used.Should().Be(1);
        response.Value.Free.Should().Be(9);
    }
}
