﻿using UsersManagement.Application.Common.Exceptions;
using UsersManagement.Application.Common.Models;
using UsersManagement.Application.Management.Commands.RegisterUsers;
using UsersManagement.Domain.Enums;

namespace UsersManagement.Application.FunctionalTests.Management.Commands;
using static Testing;

public class RegisterUsersTests
{
    [Test]
    public async Task ShouldRequireMinimumFields()
    {
        var command = new RegisterUsersCommand();

        await FluentActions.Invoking(() =>
            SendAsync(command)).Should().ThrowAsync<ValidationException>();
    }

    [Test]
    public async Task ShouldRequireAllUsersPropertiesSet()
    {
        var command = new RegisterUsersCommand()
        {
            Users = new List<UserRegistration>
            {
                new UserRegistration
                {
                    Email = "<EMAIL>",
                    Role = UserRole.Administrator
                },
                new UserRegistration
                {
                    Email = "<EMAIL>",
                },
                new UserRegistration
                {
                    Role = UserRole.Administrator
                }
            }
        };

        await FluentActions.Invoking(() =>
            SendAsync(command)).Should().ThrowAsync<ValidationException>();
    }

    [Test]
    public async Task ShouldRequireValidEmail()
    {
        var command = new RegisterUsersCommand()
        {
            Users = new List<UserRegistration>
            {
                new UserRegistration
                {
                    Email = "abc",
                    Role = UserRole.Administrator
                }
            }
        };

        await FluentActions.Invoking(() =>
            SendAsync(command)).Should().ThrowAsync<ValidationException>();
    }

    [Test]
    public async Task ShouldRegisterUsers()
    {
        var command = new RegisterUsersCommand()
        {
            Users = new List<UserRegistration>
            {
                new UserRegistration
                {
                    Email = "<EMAIL>",
                    Role = UserRole.Administrator,
                    Permissions = ["GREY"]
                }
            }
        };

        var response = await SendAsync(command);

        response.Should().NotBeNull();
        response.IsSuccess.Should().BeTrue();
    }

    [Test]
    public async Task ShouldFailReturnsError()
    {
        var command = new RegisterUsersCommand()
        {
            Users = new List<UserRegistration>
            {
                new UserRegistration
                {
                    Email = "<EMAIL>",
                    Role = UserRole.Administrator,
                    Permissions = ["GREY"]
                }
            }
        };

        var response = await SendAsync(command);

        response.Should().NotBeNull();
        response.IsFailure.Should().BeTrue();
        response.Error.Message.Should().Contain(command.Users.First().Email);
    }
}
