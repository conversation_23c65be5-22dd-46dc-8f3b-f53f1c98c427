﻿namespace UsersManagement.Application.FunctionalTests.Management.Commands;

using UsersManagement.Application.Common.Exceptions;
using UsersManagement.Application.Management.Commands.EditUser;
using static Testing;

public class EditUserTests
{
    [Test]
    public async Task ShouldRequireMinimumFields()
    {
        var command = new EditUserCommand();

        await FluentActions.Invoking(() =>
            SendAsync(command)).Should().ThrowAsync<ValidationException>();
    }

    [Test]
    public async Task ShouldRequireValidEmail()
    {
        var command = new EditUserCommand()
        {
            Email = "abc"
        };

        await FluentActions.Invoking(() =>
            SendAsync(command)).Should().ThrowAsync<ValidationException>();
    }

    [Test]
    public async Task ShouldRequireAtLeastOneUpdatablePropertyFields()
    {
        var command = new EditUserCommand()
        {
            Email = "<EMAIL>"
        };

        await FluentActions.Invoking(() =>
            SendAsync(command)).Should().ThrowAsync<ValidationException>();
    }


    [Test]
    public async Task ShouldEditUser()
    {
        var command = new EditUserCommand()
        {
            Email = "<EMAIL>",
            Request = new()
            {
                NewEmail = "<EMAIL>"
            }
        };

        var response = await SendAsync(command);

        response.Should().NotBeNull();
        response.IsSuccess.Should().BeTrue();
    }
}
