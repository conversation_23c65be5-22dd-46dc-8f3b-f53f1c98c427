﻿using UsersManagement.Application.Common.Interfaces;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Amazon.CognitoIdentityProvider;
using Amazon.CognitoIdentityProvider.Model;
using UsersManagement.Infrastructure.Settings;
using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.Model;
using UsersManagement.Infrastructure.Licenses;
using UsersManagement.Infrastructure.Identity;
using UsersManagement.Domain.Enums;

namespace UsersManagement.Application.FunctionalTests;

public class CustomWebApplicationFactory : WebApplicationFactory<Program>
{
    private static readonly AdminInitiateAuthResponse AuthenticatedResponse = new()
    {
        AuthenticationResult = new()
        {
            AccessToken = "abc",
            ExpiresIn = 1,
            IdToken = "abc",
            RefreshToken = "abc",
            TokenType = "Bearer"
        },
        ContentLength = 1,
        HttpStatusCode = System.Net.HttpStatusCode.OK
    };

    private static readonly AdminInitiateAuthResponse ChangePasswordResponse = new()
    {
        ChallengeName = ChallengeNameType.NEW_PASSWORD_REQUIRED,
        ContentLength = 1,
        HttpStatusCode = System.Net.HttpStatusCode.OK,
        Session = "12345678912345678912"
    };

    private static readonly AdminRespondToAuthChallengeResponse ChallengeOkResponse = new()
    {
        ContentLength = 1,
        HttpStatusCode = System.Net.HttpStatusCode.OK,
        AuthenticationResult = new()
        {
            AccessToken = "abc",
            ExpiresIn = 1,
            IdToken = "abc",
            RefreshToken = "abc",
            TokenType = "Bearer"
        }
    };

    private static readonly AdminRespondToAuthChallengeResponse ChallengeAgainResponse = new()
    {
        ChallengeName = ChallengeNameType.SOFTWARE_TOKEN_MFA,
        ContentLength = 1,
        HttpStatusCode = System.Net.HttpStatusCode.OK,
        Session = "12345678912345678912"
    };

    private static readonly AdminCreateUserResponse CreateUserOkResponse = new()
    {
        ContentLength = 1,
        HttpStatusCode = System.Net.HttpStatusCode.OK
    };

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureTestServices(services =>
        {
            Mock<IAmazonCognitoIdentityProvider> cognitoIdp = new();
            cognitoIdp
                .Setup(x => x.AdminInitiateAuthAsync(It.Is<AdminInitiateAuthRequest>(req => req.AuthParameters["USERNAME"] == "<EMAIL>"), It.IsAny<CancellationToken>()))
                .ReturnsAsync(AuthenticatedResponse);
            cognitoIdp
                .Setup(x => x.AdminInitiateAuthAsync(It.Is<AdminInitiateAuthRequest>(req => req.AuthParameters["USERNAME"] == "<EMAIL>"), It.IsAny<CancellationToken>()))
                .ReturnsAsync(ChangePasswordResponse);
            cognitoIdp
                .Setup(x => x.AdminRespondToAuthChallengeAsync(It.Is<AdminRespondToAuthChallengeRequest>(req => req.ChallengeName == "OkChallenge"), It.IsAny<CancellationToken>()))
                .ReturnsAsync(ChallengeOkResponse);
            cognitoIdp
                .Setup(x => x.AdminRespondToAuthChallengeAsync(It.Is<AdminRespondToAuthChallengeRequest>(req => req.ChallengeName == "AnotherChallenge"), It.IsAny<CancellationToken>()))
                .ReturnsAsync(ChallengeAgainResponse);

            cognitoIdp
                .Setup(x => x.ListUserPoolsAsync(It.IsAny<ListUserPoolsRequest>(), It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(new ListUserPoolsResponse
                {
                    ContentLength = 1,
                    HttpStatusCode = System.Net.HttpStatusCode.OK,
                    NextToken = "abc",
                    UserPools = new()
                    {
                        new()
                        {
                            Id = "test_arg",
                            CreationDate = DateTime.Now,
                            Name = "test-arg"
                        }
                    }
                }));

            cognitoIdp
                .Setup(x => x.AdminCreateUserAsync(It.Is<AdminCreateUserRequest>(x => x.Username == "<EMAIL>"), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateUserOkResponse);
            cognitoIdp
                .Setup(x => x.AdminCreateUserAsync(It.Is<AdminCreateUserRequest>(x => x.Username == "<EMAIL>"), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new NotAuthorizedException(string.Empty));
            cognitoIdp
                .Setup(x => x.AdminGetUserAsync(It.Is<AdminGetUserRequest>(x => x.Username == "<EMAIL>"), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new AdminGetUserResponse
                {
                    Username = "<EMAIL>",
                    ContentLength = 1,
                    Enabled = true,
                    HttpStatusCode = System.Net.HttpStatusCode.OK,
                    UserStatus = UserStatusType.CONFIRMED,
                    UserAttributes = new List<AttributeType>
                    {
                        new()
                        {
                            Name = "email",
                            Value = "<EMAIL>"
                        },
                        new()
                        {
                            Name = CognitoConstants.Attributes.ROLE,
                            Value = UserRole.Standard.ToString()
                        },
                        new()
                        {
                            Name = CognitoConstants.Attributes.ORIGIN,
                            Value = UserOrigin.Standalone.ToString()
                        },
                        new()
                        {
                            Name = CognitoConstants.Attributes.PERMISSIONS,
                            Value = "GREY,RED"
                        }
                    }
                });
            cognitoIdp
                .Setup(x => x.AdminUpdateUserAttributesAsync(It.IsAny<AdminUpdateUserAttributesRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new AdminUpdateUserAttributesResponse
                {
                    ContentLength = 1,
                    HttpStatusCode = System.Net.HttpStatusCode.OK,
                    ResponseMetadata = new()
                });
                

            services.RemoveAll<IAmazonCognitoIdentityProvider>()
                .AddTransient(provider => cognitoIdp.Object);


            Mock<ITenantProvider> tenantProvider = new();
            tenantProvider.Setup(x => x.GetTenantName()).Returns("test");
            tenantProvider.Setup(x => x.GetTenantCountry()).Returns("arg");
            tenantProvider.Setup(x => x.GetTenantId()).Returns("test-tenant");

            services.RemoveAll<ITenantProvider>().AddScoped(provider => tenantProvider.Object);

            Mock<IAmazonDynamoDB> dynamoDb = new();

            dynamoDb
                .Setup(x => x.GetItemAsync(It.Is<GetItemRequest>(x => x.TableName == "ai-licenses"), It.IsAny<CancellationToken>()))
                .ReturnsAsync((GetItemRequest request, CancellationToken cancellationToken) =>
                {
                    return new GetItemResponse
                    {
                        ConsumedCapacity = new(),
                        ContentLength = 1,
                        HttpStatusCode = System.Net.HttpStatusCode.OK,
                        Item = new()
                        {
                            {"total", new AttributeValue { S = "10" } },
                            {"used", new AttributeValue { S = "1" } },
                            {"expiration_date", new AttributeValue { S = DateTime.UtcNow.AddDays(10).ToString("s") } },
                        }
                    };
                });

            dynamoDb
                .Setup(x => x.QueryAsync(It.Is<QueryRequest>(x => x.TableName == "permissions"), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new QueryResponse
                {
                    ConsumedCapacity = new(),
                    ContentLength = 1,
                    Count = 1,
                    HttpStatusCode = System.Net.HttpStatusCode.OK,
                    Items = [
                        new Dictionary<string, AttributeValue>()
                        {
                            {"tenant_id", new("test-tenant") },
                            {"color", new("#3f3f3f") },
                            {"title_en", new("Grey") },
                            {"title_es", new("Gris") },
                            {"title_pt", new("Cinza") },
                            {"users_count", new() { N = "1"} },
                            {"value", new("GREY") },
                        }
                    ]
                });

            dynamoDb
                //.Setup(x => x.UpdateItemAsync(It.Is<UpdateItemRequest>(x => x.TableName == "ai-licenses"), It.IsAny<CancellationToken>()))
                .Setup(x => x.UpdateItemAsync(It.IsAny<UpdateItemRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new UpdateItemResponse
                {
                    ConsumedCapacity = new(),
                    ContentLength = 1,
                    HttpStatusCode = System.Net.HttpStatusCode.OK,
                    Attributes = new()
                });

            services
                .RemoveAll<IAmazonDynamoDB>()
                .AddTransient(provider => dynamoDb.Object);

            Mock<ISettingsApi> settingsApiMock = new();
            settingsApiMock
                .Setup(x => x.GetConfig(It.IsAny<IDictionary<string, string>>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new KeyValuePair<string, string>("key", "secret"));

            services.RemoveAll<ISettingsApi>().AddTransient(provider => settingsApiMock.Object);


            Mock<ILicenseApi> licenseApiMock = new();
            licenseApiMock
                .Setup(x => x.Consume(It.IsAny<IDictionary<string, string>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new Refit.ApiResponse<object>(new HttpResponseMessage(System.Net.HttpStatusCode.OK), null, new(), null));

            licenseApiMock
                .Setup(x => x.Get(It.IsAny<IDictionary<string, string>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((IDictionary<string, string> tenantId, CancellationToken cancellationToken) => new Domain.DTO.License(tenantId["X-Tenant-Id"], 10, 1, DateTime.UtcNow.AddDays(1)));

            services.RemoveAll<ILicenseApi>().AddTransient(provider => licenseApiMock.Object);
        });
    }
}
