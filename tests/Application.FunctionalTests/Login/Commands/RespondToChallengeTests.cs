﻿namespace UsersManagement.Application.FunctionalTests.Login.Commands;

using Amazon.CognitoIdentityProvider;
using UsersManagement.Application.Common.Exceptions;
using UsersManagement.Application.Login.Commands.RespondToChallenge;
using static Testing;

public class RespondToChallengeTests
{
    [Test]
    public async Task ShouldRequireMinimumFields()
    {
        var command = new RespondToChallengeCommand();

        await FluentActions.Invoking(() =>
            SendAsync(command)).Should().ThrowAsync<ValidationException>();
    }

    [Test]
    public async Task ShouldRequireValidEmail()
    {
        var command = new RespondToChallengeCommand();

        await FluentActions.Invoking(() =>
            SendAsync(command)).Should().ThrowAsync<ValidationException>();
    }

    [Test]
    public async Task ShouldPassChallengeOk()
    {
        var command = new RespondToChallengeCommand()
        {
            Email = "<EMAIL>",
            ChallengeName = "OkChallenge",
            Parameters = new()
            {
                { "CHALLENGE_PARAMETER", "value" }
            },
            Session = "Test"
        };

        var response = await SendAsync(command);

        response.Should().NotBeNull();
        response.IsSuccess.Should().BeTrue();
        response.Value.Should().NotBeNull();

        response.Value.TenantId.Should().NotBeNullOrWhiteSpace();
        response.Value.AccessToken.Should().NotBeNullOrWhiteSpace();
        response.Value.RefreshToken.Should().NotBeNullOrWhiteSpace();
        response.Value.ChallengeName.Should().BeNull();
        response.Value.Session.Should().BeNullOrWhiteSpace();
    }

    [Test]
    public async Task ShouldRequireAnotherChallenge()
    {
        var command = new RespondToChallengeCommand()
        {
            Email = "<EMAIL>",
            ChallengeName = "AnotherChallenge",
            Parameters = new()
            {
                { "CHALLENGE_PARAMETER", "value" }
            },
            Session = "Test"
        };

        var response = await SendAsync(command);

        response.Should().NotBeNull();
        response.IsSuccess.Should().BeTrue();
        response.Value.Should().NotBeNull();

        response.Value.TenantId.Should().NotBeNullOrWhiteSpace();
        response.Value.AccessToken.Should().BeNull();
        response.Value.ChallengeName.Should().Be(ChallengeNameType.SOFTWARE_TOKEN_MFA.Value);
        response.Value.Session.Should().NotBeNull();
    }
}
