﻿using Amazon.CognitoIdentityProvider;
using UsersManagement.Application.Common.Exceptions;
using UsersManagement.Application.Login.Commands.UserLogin;

namespace UsersManagement.Application.FunctionalTests.Login.Commands;

using static Testing;

public class UserLoginTests
{
    [Test]
    public async Task ShouldRequireMinimumFields()
    {
        var command = new UserLoginCommand();

        await FluentActions.Invoking(() =>
            SendAsync(command)).Should().ThrowAsync<ValidationException>();
    }

    [Test]
    public async Task ShouldRequireValidEmail()
    {
        var command = new UserLoginCommand()
        {
            Email = "asd",
            Password = "password"
        };

        await FluentActions.Invoking(() =>
            SendAsync(command)).Should().ThrowAsync<ValidationException>();
    }

    [Test]
    public async Task ShouldLogin()
    {
        var command = new UserLoginCommand()
        {
            Email = "<EMAIL>",
            Password = "ABCD1234"
        };

        var response = await SendAsync(command);

        response.Should().NotBeNull();
        response.IsSuccess.Should().BeTrue();
        response.Value.Should().NotBeNull();

        response.Value.TenantId.Should().NotBeNullOrWhiteSpace();
        response.Value.TenantId.Should().Be("test_arg");
        response.Value.AccessToken.Should().NotBeNullOrWhiteSpace();
        response.Value.RefreshToken.Should().NotBeNullOrWhiteSpace();
        response.Value.ChallengeName.Should().BeNull();
        response.Value.Session.Should().BeNullOrWhiteSpace();
    }

    [Test]
    public async Task ShouldFireChangePasswordChallenge()
    {
        var command = new UserLoginCommand()
        {
            Email = "<EMAIL>",
            Password = "ABCD1234"
        };

        var response = await SendAsync(command);

        response.Should().NotBeNull();
        response.IsSuccess.Should().BeTrue();
        response.Value.Should().NotBeNull();

        response.Value.TenantId.Should().NotBeNullOrWhiteSpace();
        response.Value.TenantId.Should().Be("test_arg");
        response.Value.AccessToken.Should().BeNull();
        response.Value.ChallengeName.Should().Be(ChallengeNameType.NEW_PASSWORD_REQUIRED.Value);
        response.Value.Session.Should().NotBeNull();
    }
}
