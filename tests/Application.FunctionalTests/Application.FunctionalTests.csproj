﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <RootNamespace>UsersManagement.Application.FunctionalTests</RootNamespace>
        <AssemblyName>UsersManagement.Application.FunctionalTests</AssemblyName>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" />
        <PackageReference Include="nunit" />
        <PackageReference Include="NUnit.Analyzers">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="NUnit3TestAdapter" />
        <PackageReference Include="coverlet.collector">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="FluentAssertions" />
        <PackageReference Include="Moq" />
        <PackageReference Include="Respawn" />
    </ItemGroup>
    
    <ItemGroup>
        <ProjectReference Include="..\..\src\UsersManagement.API\UsersManagement.API.csproj" />
    </ItemGroup>

</Project>
