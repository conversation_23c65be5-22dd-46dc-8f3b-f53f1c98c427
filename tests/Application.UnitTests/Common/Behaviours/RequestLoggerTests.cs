﻿using UsersManagement.Application.Common.Behaviours;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using UsersManagement.Application.Login.Commands.UserLogin;

namespace UsersManagement.Application.UnitTests.Common.Behaviours;

public class RequestLoggerTests
{
    private Mock<ILogger<UserLoginCommand>> _logger = null!;

    [SetUp]
    public void Setup()
    {
        _logger = new Mock<ILogger<UserLoginCommand>>();
    }

    [Test]
    public async Task ShouldCallLoggerLogOnlyOnce()
    {
        var requestLogger = new LoggingBehaviour<UserLoginCommand>(_logger.Object);

        await requestLogger.Process(new UserLoginCommand { Email = "<EMAIL>", Password = "abcd" }, new CancellationToken());

        _logger.Verify(x => x.Log(
            It.IsAny<LogLevel>(),
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            (Func<It.IsAnyType, Exception?, string>)It.IsAny<object>()), Times.Once);
    }
}
