﻿using N5.Result;
using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Application.Management.Queries.GetUsers;
using UsersManagement.Domain.Entities;
using UsersManagement.Domain.Enums;

namespace UsersManagement.Application.UnitTests.Management.Queries;

public class GetUsersTests
{
    private Mock<IIdentityService> _identityServiceMock;
    private GetUsersCommandHandler _handler;

    [SetUp]
    public void SetUp()
    {
        _identityServiceMock = new Mock<IIdentityService>(MockBehavior.Strict);
        _handler = new GetUsersCommandHandler(_identityServiceMock.Object);
    }

    [Test]
    public async Task Handle_WhenIdentityServiceReturnsFailure_ReturnsFailure()
    {
        // Arrange
        var command = new GetUsersCommand { Cursor = "cur", Limit = 5 };
        var error = new Error("ERR", "fail");
        _identityServiceMock
            .Setup(svc => svc.GetUsersAsync(It.IsAny<string?>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure<(string paginationToken, List<User> users)>(error));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(error);
    }

    [Test]
    public async Task Handle_WhenIdentityServiceReturnsSuccess_ReturnsMappedResponse()
    {
        // Arrange
        var usersList = new List<User>
        {
            User.Map("John", "Doe", "<EMAIL>", UserRole.Standard, true, UserOrigin.Standalone, new List<string>())
        };
        var pagination = "nextCursor";
        _identityServiceMock
            .Setup(svc => svc.GetUsersAsync(It.IsAny<string?>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success<(string, List<User>)>((pagination, usersList)));

        var command = new GetUsersCommand { Cursor = null, Limit = 10 };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        var response = result.Value;
        response.Users.Should().HaveCount(1);
        var dto = response.Users.First();
        dto.Email.Should().Be("<EMAIL>");
        response.Cursor.Should().Be(pagination);
    }
}
