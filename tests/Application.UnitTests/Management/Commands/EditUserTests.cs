﻿using N5.Result;
using static UsersManagement.Domain.Errors.CommandsErrors;
using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Application.Management.Commands.EditUser;
using UsersManagement.Domain.DTO;
using UsersManagement.Domain.Entities;

namespace UsersManagement.Application.UnitTests.Management.Commands;

public class EditUserTests
{
    private Mock<IIdentityService> _identityServiceMock;
    private Mock<IPermissionsService> _permissionsServiceMock;
    private Mock<ITenantProvider> _tenantProviderMock;
    private EditUserCommandHandler _handler;
    private const string Email = "<EMAIL>";
    private const string TenantId = "tenant1";
    private readonly CancellationToken _ct = CancellationToken.None;

    [SetUp]
    public void SetUp()
    {
        _identityServiceMock = new Mock<IIdentityService>(MockBehavior.Strict);
        _permissionsServiceMock = new Mock<IPermissionsService>(MockBehavior.Strict);
        _tenantProviderMock = new Mock<ITenantProvider>(MockBehavior.Strict);
        _handler = new EditUserCommandHandler(
            _identityServiceMock.Object,
            _permissionsServiceMock.Object,
            _tenantProviderMock.Object);
    }

    [Test]
    public async Task Handle_GetUserFails_ReturnsFailure()
    {
        // Arrange
        var error = new Error("ERR", "get user fail");
        _tenantProviderMock.Setup(tp => tp.GetTenantId()).Returns(TenantId);
        _identityServiceMock
            .Setup(s => s.GetUserAsync(Email, _ct))
            .ReturnsAsync(Result.Failure<User>(error));

        var command = new EditUserCommand { Email = Email, Request = new UpdateUserRequest() };

        // Act
        var result = await _handler.Handle(command, _ct);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(error);
    }

    [Test]
    public async Task Handle_NoPermissionChange_EditUserSucceeds_ReturnsSuccess()
    {
        // Arrange
        var user = User.Create(null, null, Email, Domain.Enums.UserRole.Standard, Domain.Enums.UserOrigin.Standalone, new List<string>());
        _tenantProviderMock.Setup(tp => tp.GetTenantId()).Returns(TenantId);
        _identityServiceMock
            .Setup(s => s.GetUserAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(user));

        _identityServiceMock
            .Setup(s => s.EditUserAsync(It.IsAny<string>(), It.IsAny<UpdateUserRequest>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());

        var command = new EditUserCommand { Email = Email, Request = new UpdateUserRequest() };

        // Act
        var result = await _handler.Handle(command, _ct);

        // Assert
        result.IsSuccess.Should().BeTrue();
        _permissionsServiceMock.VerifyNoOtherCalls();
    }

    [Test]
    public async Task Handle_NoPermissionChange_EditUserFails_ReturnsFailure()
    {
        // Arrange
        var user = User.Create(null, null, Email, Domain.Enums.UserRole.Standard, Domain.Enums.UserOrigin.Standalone, new List<string>());
        var error = new Error("ERR2", "edit fail");
        _tenantProviderMock.Setup(tp => tp.GetTenantId()).Returns(TenantId);
        _identityServiceMock
            .Setup(s => s.GetUserAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(user));
        _identityServiceMock
            .Setup(s => s.EditUserAsync(It.IsAny<string>(), It.IsAny<UpdateUserRequest>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure(error));

        var command = new EditUserCommand { Email = Email, Request = new UpdateUserRequest() };

        // Act
        var result = await _handler.Handle(command, _ct);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(error);
    }

    [Test]
    public async Task Handle_PermissionCheckFails_ReturnsFailure()
    {
        // Arrange
        var user = User.Create(null, null, Email, Domain.Enums.UserRole.Standard, Domain.Enums.UserOrigin.Standalone, new List<string>());
        var request = new UpdateUserRequest { Permissions = new List<string> { "p1" } };
        var error = new Error("ERR3", "exists fail");
        _tenantProviderMock.Setup(tp => tp.GetTenantId()).Returns(TenantId);
        _identityServiceMock
            .Setup(s => s.GetUserAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(user));
        _permissionsServiceMock
            .Setup(p => p.ExistsPermissions(It.IsAny<string>(), It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure<bool>(error));

        var command = new EditUserCommand { Email = Email, Request = request };

        // Act
        var result = await _handler.Handle(command, _ct);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(error);
    }

    [Test]
    public async Task Handle_PermissionsNotExist_ReturnsPermissionsError()
    {
        // Arrange
        var user = User.Create(null, null, Email, Domain.Enums.UserRole.Standard, Domain.Enums.UserOrigin.Standalone, new List<string>());
        var request = new UpdateUserRequest { Permissions = new List<string> { "p1" } };
        _tenantProviderMock.Setup(tp => tp.GetTenantId()).Returns(TenantId);
        _identityServiceMock
            .Setup(s => s.GetUserAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(user));
        _permissionsServiceMock
            .Setup(p => p.ExistsPermissions(It.IsAny<string>(), It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(false));

        var command = new EditUserCommand { Email = Email, Request = request };

        // Act
        var result = await _handler.Handle(command, _ct);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(PermissionsError.PERMISSIONS_ERROR);
    }

    [Test]
    public async Task Handle_EditUserFailsAfterPermissionCheck_ReturnsFailure()
    {
        // Arrange
        var oldPerms = new List<string> { "p1" };
        
        var user = User.Create(null, null, Email, Domain.Enums.UserRole.Standard, Domain.Enums.UserOrigin.Standalone, oldPerms);

        var request = new UpdateUserRequest { Permissions = new List<string> { "p1" } };
        var error = new Error("ERR4", "edit fail");
        _tenantProviderMock.Setup(tp => tp.GetTenantId()).Returns(TenantId);
        _identityServiceMock
            .Setup(s => s.GetUserAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(user));
        _permissionsServiceMock
            .Setup(p => p.ExistsPermissions(It.IsAny<string>(), It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(true));
        _identityServiceMock
            .Setup(s => s.EditUserAsync(It.IsAny<string>(), It.IsAny<UpdateUserRequest>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure(error));

        var command = new EditUserCommand { Email = Email, Request = request };

        // Act
        var result = await _handler.Handle(command, _ct);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(error);
    }

    [Test]
    public async Task Handle_EditUserSucceeds_WithPermissionDiff_CallsAddPermissionUserCount()
    {
        // Arrange
        var oldPerms = new List<string> { "p1", "p2" };
        var newPerms = new List<string> { "p2", "p3" };
        var user = User.Create(null, null, Email, Domain.Enums.UserRole.Standard, Domain.Enums.UserOrigin.Standalone, oldPerms);
        var request = new UpdateUserRequest { Permissions = newPerms };
        _tenantProviderMock.Setup(tp => tp.GetTenantId()).Returns(TenantId);
        _identityServiceMock
            .Setup(s => s.GetUserAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(user));
        _permissionsServiceMock
            .Setup(p => p.ExistsPermissions(It.IsAny<string>(), It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(true));
        _identityServiceMock
            .Setup(s => s.EditUserAsync(It.IsAny<string>(), It.IsAny<UpdateUserRequest>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());
        _permissionsServiceMock
            .Setup(p => p.AddPermissionUserCount(It.IsAny<string>(), It.Is<List<string>>(l => l.SequenceEqual(new[] { "p1" })), -1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());
        _permissionsServiceMock
            .Setup(p => p.AddPermissionUserCount(It.IsAny<string>(), It.Is<List<string>>(l => l.SequenceEqual(new[] { "p3" })), 1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());

        var command = new EditUserCommand { Email = Email, Request = request };

        // Act
        var result = await _handler.Handle(command, _ct);

        // Assert
        result.IsSuccess.Should().BeTrue();
        _permissionsServiceMock.VerifyAll();
    }

}
