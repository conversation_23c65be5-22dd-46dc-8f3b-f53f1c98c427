﻿using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Threading;
using Microsoft.Extensions.Logging;
using Moq;
using N5.Result;
using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Application.Common.Models;
using UsersManagement.Application.Login.Commands.ExternalLogin;
using UsersManagement.Domain.Entities;

namespace UsersManagement.Application.UnitTests.Login.Commands;
public class ExternalLoginTests
{
    private Mock<ITenantProvider> _tenantProviderMock;
    private Mock<IIdentityService> _identityServiceMock;
    private Mock<ISettingsService> _settingsServiceMock;
    private Mock<IPasswordGenerator> _passwordGeneratorMock;
    private Mock<IExternalJwtValidator> _externalJwtValidatorMock;
    private Mock<ILicenseService> _licenseServiceMock;
    private Mock<ILogger<ExternalLoginCommandHandler>> _externalLoginCommandHandlerLoggerMock;
    private ExternalLoginCommandHandler _handler;

    [SetUp]
    public void Setup()
    {
        _tenantProviderMock = new Mock<ITenantProvider>();
        _identityServiceMock = new Mock<IIdentityService>();
        _settingsServiceMock = new Mock<ISettingsService>();
        _passwordGeneratorMock = new Mock<IPasswordGenerator>();
        _externalJwtValidatorMock = new Mock<IExternalJwtValidator>();
        _licenseServiceMock = new();
        _externalLoginCommandHandlerLoggerMock = new();
        _handler = new ExternalLoginCommandHandler(
            _tenantProviderMock.Object,
            _identityServiceMock.Object,
            _settingsServiceMock.Object,
            _passwordGeneratorMock.Object,
            _externalJwtValidatorMock.Object,
            _licenseServiceMock.Object,
            _externalLoginCommandHandlerLoggerMock.Object
        );
    }

    [Test]
    public async Task Handle_NewUserCreation_Success()
    {
        // Arrange
        var command = new ExternalLoginCommand() { LegacyJwt = "legacyJwtToken" };
        _tenantProviderMock.Setup(x => x.GetTenantName()).Returns("TenantName");
        _tenantProviderMock.Setup(x => x.GetTenantCountry()).Returns("TenantCountry");

        // Simula la obtención del tenantId
        _identityServiceMock.Setup(x => x.GetTenantIdByNameAndCountry("TenantName", "TenantCountry", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("tenant-id-123"));

        // Simula la obtención del JWK
        _settingsServiceMock.Setup(x => x.GetJWKAsync("tenant-id-123", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("jwkJsonString"));

        // Simula la validación del JWT y que el claim email y roles existan
        var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Email, "<EMAIL>"),
            new Claim("roles", "[Administrator]")
        };
        var claimsIdentity = new ClaimsIdentity(claims);
        _externalJwtValidatorMock.Setup(x => x.ValidateJwtByJwkAsync("jwkJsonString", "legacyJwtToken"))
            .ReturnsAsync(Result.Success(claimsIdentity));

        // Simula que el usuario no existe
        _identityServiceMock.Setup(x => x.UserExists("tenant-id-123", "<EMAIL>", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(false));

        // La generación de contraseña retorna una cadena fija
        _passwordGeneratorMock.Setup(x => x.Generate()).Returns("generatedPassword");

        // Simula la creación de usuario en el idp
        _identityServiceMock.Setup(x => x.CreateUsersAsync("tenant-id-123", It.IsAny<IList<User>>(), "generatedPassword", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success<(int createdQty, string? errors)>((1, null)));

        _licenseServiceMock.Setup(x => x.Consume(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());

        // Simula un login exitoso sin challenge (flujo normal)
        var loginResponse = new LoginResponse(null, null, "tenant-id-123", "accessToken", "refreshToken");

        _identityServiceMock.Setup(x => x.LoginAsync("tenant-id-123", "<EMAIL>", "generatedPassword", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(loginResponse));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeEquivalentTo(loginResponse);

        _licenseServiceMock.Verify(x => x.Consume(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
        _licenseServiceMock.Verify(x => x.Refund(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Test]
    public async Task Handle_NewUserCreation_WithNonAdminRole_CreatesUserWithStandardRole()
    {
        // Arrange
        var command = new ExternalLoginCommand { LegacyJwt = "legacyJwtToken" };
        _tenantProviderMock.Setup(x => x.GetTenantName()).Returns("TenantName");
        _tenantProviderMock.Setup(x => x.GetTenantCountry()).Returns("TenantCountry");

        _identityServiceMock
            .Setup(x => x.GetTenantIdByNameAndCountry("TenantName", "TenantCountry", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("tenant-id-123"));

        _settingsServiceMock
            .Setup(x => x.GetJWKAsync("tenant-id-123", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("jwkJsonString"));

        // Se proveen claims con email y un claim "roles" que NO contiene "Administrator"
        var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Email, "<EMAIL>"),
            new Claim("roles", "[Standard]") // No contiene "Administrator"
        };
        var claimsPrincipal = new ClaimsIdentity(claims);
        _externalJwtValidatorMock
            .Setup(x => x.ValidateJwtByJwkAsync("jwkJsonString", "legacyJwtToken"))
            .ReturnsAsync(Result.Success(claimsPrincipal));

        // Se simula que el usuario no existe
        _identityServiceMock
            .Setup(x => x.UserExists("tenant-id-123", "<EMAIL>", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(false));

        _passwordGeneratorMock.Setup(x => x.Generate()).Returns("generatedPassword");

        User? capturedUser = null;
        _identityServiceMock
            .Setup(x => x.CreateUsersAsync("tenant-id-123", It.IsAny<List<User>>(), "generatedPassword", It.IsAny<CancellationToken>()))
            .Callback<string, IList<User>, string, CancellationToken>((tenantId, users, pwd, token) =>
            {
                capturedUser = users.First();
            })
            .ReturnsAsync(Result.Success<(int createdQty, string? errors)>((1, null)));

        var loginResponse = new LoginResponse(null, null, "tenant-id-123", "accessToken", "refreshToken");
        _identityServiceMock
            .Setup(x => x.LoginAsync("tenant-id-123", "<EMAIL>", "generatedPassword", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(loginResponse));
        
        _licenseServiceMock.Setup(x => x.Consume(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeEquivalentTo(loginResponse);
        capturedUser!.Should().NotBeNull();
        capturedUser!.Role.Should().Be(Domain.Enums.UserRole.Standard);
        
        _licenseServiceMock.Verify(x => x.Consume(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
        _licenseServiceMock.Verify(x => x.Refund(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Test]
    public async Task Handle_ExistingUser_Success()
    {
        // Arrange
        var command = new ExternalLoginCommand() { LegacyJwt = "legacyJwtToken" };
        _tenantProviderMock.Setup(x => x.GetTenantName()).Returns("TenantName");
        _tenantProviderMock.Setup(x => x.GetTenantCountry()).Returns("TenantCountry");

        _identityServiceMock.Setup(x => x.GetTenantIdByNameAndCountry("TenantName", "TenantCountry", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("tenant-id-123"));

        _settingsServiceMock.Setup(x => x.GetJWKAsync("tenant-id-123", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("jwkJsonString"));

        var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Email, "<EMAIL>"),
            new Claim("roles", "[Standard]")
        };
        var claimsIdentity = new ClaimsIdentity(claims);
        _externalJwtValidatorMock.Setup(x => x.ValidateJwtByJwkAsync("jwkJsonString", "legacyJwtToken"))
            .ReturnsAsync(Result.Success(claimsIdentity));

        // Simula que el usuario ya existe
        _identityServiceMock.Setup(x => x.UserExists("tenant-id-123", "<EMAIL>", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(true));

        _passwordGeneratorMock.Setup(x => x.Generate()).Returns("generatedPassword");

        // Para usuario existente se fuerza el cambio de contraseña
        _identityServiceMock.Setup(x => x.ForceChangePassword("tenant-id-123", "<EMAIL>", "generatedPassword", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(true));

        var loginResponse = new LoginResponse(null, null, "tenant-id-123", "accessToken", "refreshToken");
        _identityServiceMock.Setup(x => x.LoginAsync("tenant-id-123", "<EMAIL>", "generatedPassword", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(loginResponse));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeEquivalentTo(loginResponse);
    }

    [Test]
    public async Task Handle_ChallengeFlow_Success()
    {
        // Arrange
        var command = new ExternalLoginCommand { LegacyJwt = "legacyJwtToken" };
        _tenantProviderMock.Setup(x => x.GetTenantName()).Returns("TenantName");
        _tenantProviderMock.Setup(x => x.GetTenantCountry()).Returns("TenantCountry");

        _identityServiceMock.Setup(x => x.GetTenantIdByNameAndCountry("TenantName", "TenantCountry", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("tenant-id-123"));

        _settingsServiceMock.Setup(x => x.GetJWKAsync("tenant-id-123", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("jwkJsonString"));

        var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Email, "<EMAIL>"),
            new Claim("roles", "[Standard]")
        };

        var claimsIdentity = new ClaimsIdentity(claims);
        _externalJwtValidatorMock.Setup(x => x.ValidateJwtByJwkAsync("jwkJsonString", "legacyJwtToken"))
            .ReturnsAsync(Result.Success(claimsIdentity));

        _identityServiceMock.Setup(x => x.UserExists("tenant-id-123", "<EMAIL>", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(true));

        _passwordGeneratorMock.Setup(x => x.Generate()).Returns("generatedPassword");

        _identityServiceMock.Setup(x => x.ForceChangePassword("tenant-id-123", "<EMAIL>", "generatedPassword", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(true));

        // Simula un login que requiere challenge
        var loginResponse = new LoginResponse("NEW_PASSWORD_REQUIRED", "sessionToken", "tenant-id-123", null, null);
        _identityServiceMock.Setup(x => x.LoginAsync("tenant-id-123", "<EMAIL>", "generatedPassword", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(loginResponse));

        // Simula la respuesta exitosa al challenge
        var challengeResponse = new LoginResponse(null, null, "tenant-id-123", "accessToken", "refreshToken");
        _identityServiceMock.Setup(x => x.RespondToAuthChallengeAsync(
            "tenant-id-123",
            "sessionToken",
            "<EMAIL>",
            "NEW_PASSWORD_REQUIRED",
            It.Is<Dictionary<string, string>>(d => d["NEW_PASSWORD"] == "generatedPassword"),
            It.IsAny<CancellationToken>())
        ).ReturnsAsync(Result.Success(challengeResponse));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeEquivalentTo(challengeResponse);
    }

    [Test]
    public async Task Handle_TenantIdRetrievalFails_ReturnsFailure()
    {
        // Arrange
        var command = new ExternalLoginCommand { LegacyJwt = "legacyJwtToken" };
        _tenantProviderMock.Setup(x => x.GetTenantName()).Returns("TenantName");
        _tenantProviderMock.Setup(x => x.GetTenantCountry()).Returns("TenantCountry");

        var error = new Error("TENANT_NOT_FOUND", "Tenant not found");
        _identityServiceMock.Setup(x => x.GetTenantIdByNameAndCountry("TenantName", "TenantCountry", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure<string>(error));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().BeEquivalentTo(error);
    }

    [Test]
    public async Task Handle_SettingsServiceFails_ReturnsFailure()
    {
        // Arrange
        var command = new ExternalLoginCommand { LegacyJwt = "legacyJwtToken" };
        _tenantProviderMock.Setup(x => x.GetTenantName()).Returns("TenantName");
        _tenantProviderMock.Setup(x => x.GetTenantCountry()).Returns("TenantCountry");
        _identityServiceMock.Setup(x => x.GetTenantIdByNameAndCountry("TenantName", "TenantCountry", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("tenant-id-123"));

        var error = new Error("JWK_ERROR", "JWK retrieval error");
        _settingsServiceMock.Setup(x => x.GetJWKAsync("tenant-id-123", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure<string>(error));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().BeEquivalentTo(error);
    }

    [Test]
    public async Task Handle_ExternalJwtValidatorFails_ReturnsFailure()
    {
        // Arrange
        var command = new ExternalLoginCommand { LegacyJwt = "legacyJwtToken" };
        _tenantProviderMock.Setup(x => x.GetTenantName()).Returns("TenantName");
        _tenantProviderMock.Setup(x => x.GetTenantCountry()).Returns("TenantCountry");
        _identityServiceMock.Setup(x => x.GetTenantIdByNameAndCountry("TenantName", "TenantCountry", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("tenant-id-123"));
        _settingsServiceMock.Setup(x => x.GetJWKAsync("tenant-id-123", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("jwkJsonString"));

        var error = new Error("JWT_VALIDATION_FAILED", "JWT error");

        _externalJwtValidatorMock.Setup(x => x.ValidateJwtByJwkAsync("jwkJsonString", "legacyJwtToken"))
            .ReturnsAsync(Result.Failure<ClaimsIdentity>(error));

        // Act
        var result = await _handler.Handle(command, It.IsAny<CancellationToken>());

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().BeEquivalentTo(error);
    }

    [Test]
    public async Task Handle_MissingEmailClaim_ReturnsFailure()
    {
        // Arrange: ClaimsPrincipal sin claim de email
        var command = new ExternalLoginCommand { LegacyJwt = "legacyJwtToken" };
        _tenantProviderMock.Setup(x => x.GetTenantName()).Returns("TenantName");
        _tenantProviderMock.Setup(x => x.GetTenantCountry()).Returns("TenantCountry");
        _identityServiceMock.Setup(x => x.GetTenantIdByNameAndCountry("TenantName", "TenantCountry", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("tenant-id-123"));
        _settingsServiceMock.Setup(x => x.GetJWKAsync("tenant-id-123", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("jwkJsonString"));

        // No se añade claim de email (aunque se puede incluir roles para pasar a la siguiente validación)
        var claims = new List<Claim>
        {
            new Claim("roles", "[Administrator]")
        };
        var claimsIdentity = new ClaimsIdentity(claims);
        _externalJwtValidatorMock.Setup(x => x.ValidateJwtByJwkAsync("jwkJsonString", "legacyJwtToken"))
            .ReturnsAsync(Result.Success(claimsIdentity));

        // Act
        var result = await _handler.Handle(command, It.IsAny<CancellationToken>());

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("INVALID_TOKEN_CLAIMS");
    }

    [Test]
    public async Task Handle_InvalidEmailClaim_ReturnsFailure()
    {
        // Arrange: Se provee un email no válido para la validación
        var command = new ExternalLoginCommand { LegacyJwt = "legacyJwtToken" };
        _tenantProviderMock.Setup(x => x.GetTenantName()).Returns("TenantName");
        _tenantProviderMock.Setup(x => x.GetTenantCountry()).Returns("TenantCountry");
        _identityServiceMock.Setup(x => x.GetTenantIdByNameAndCountry("TenantName", "TenantCountry", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("tenant-id-123"));
        _settingsServiceMock.Setup(x => x.GetJWKAsync("tenant-id-123", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("jwkJsonString"));

        var claims = new List<Claim>
        {
            // Email en formato inválido
            new Claim(JwtRegisteredClaimNames.Email, "invalidEmail"),
            new Claim("roles", "[Administrator]")
        };
        var claimsIdentity = new ClaimsIdentity(claims);
        _externalJwtValidatorMock.Setup(x => x.ValidateJwtByJwkAsync("jwkJsonString", "legacyJwtToken"))
            .ReturnsAsync(Result.Success(claimsIdentity));

        // Act
        var result = await _handler.Handle(command, It.IsAny<CancellationToken>());

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("INVALID_TOKEN_CLAIMS");
    }

    [Test]
    public async Task Handle_UserExistsFails_ReturnsFailure()
    {
        // Arrange
        var command = new ExternalLoginCommand { LegacyJwt = "legacyJwtToken" };
        _tenantProviderMock.Setup(x => x.GetTenantName()).Returns("TenantName");
        _tenantProviderMock.Setup(x => x.GetTenantCountry()).Returns("TenantCountry");
        _identityServiceMock.Setup(x => x.GetTenantIdByNameAndCountry("TenantName", "TenantCountry", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("tenant-id-123"));
        _settingsServiceMock.Setup(x => x.GetJWKAsync("tenant-id-123", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("jwkJsonString"));

        var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Email, "<EMAIL>"),
            new Claim("roles", "[Administrator]")
        };
        var claimsIdentity = new ClaimsIdentity(claims);
        _externalJwtValidatorMock.Setup(x => x.ValidateJwtByJwkAsync("jwkJsonString", "legacyJwtToken"))
            .ReturnsAsync(Result.Success(claimsIdentity));

        var error = new Error("USER_EXISTENCE_ERROR", "User existence error");
        _identityServiceMock.Setup(x => x.UserExists("tenant-id-123", "<EMAIL>", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure<bool>(error));

        // Act
        var result = await _handler.Handle(command, It.IsAny<CancellationToken>());

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().BeEquivalentTo(error);
    }

    [Test]
    public async Task Handle_CreateUsersFails_ReturnsFailure()
    {
        // Arrange: Flujo de nuevo usuario, pero falla la creación
        var command = new ExternalLoginCommand { LegacyJwt = "legacyJwtToken" };
        _tenantProviderMock.Setup(x => x.GetTenantName()).Returns("TenantName");
        _tenantProviderMock.Setup(x => x.GetTenantCountry()).Returns("TenantCountry");
        _identityServiceMock.Setup(x => x.GetTenantIdByNameAndCountry("TenantName", "TenantCountry", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("tenant-id-123"));
        _settingsServiceMock.Setup(x => x.GetJWKAsync("tenant-id-123", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("jwkJsonString"));

        var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Email, "<EMAIL>"),
            new Claim("roles", "[Administrator]")
        };
        var claimsIdentity = new ClaimsIdentity(claims);
        _externalJwtValidatorMock.Setup(x => x.ValidateJwtByJwkAsync("jwkJsonString", "legacyJwtToken"))
            .ReturnsAsync(Result.Success(claimsIdentity));

        // Usuario no existe
        _identityServiceMock.Setup(x => x.UserExists("tenant-id-123", "<EMAIL>", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(false));

        _passwordGeneratorMock.Setup(x => x.Generate()).Returns("generatedPassword");

        var error = new Error("CREATE_USER_ERROR", "User creation error");
        _identityServiceMock.Setup(x => x.CreateUsersAsync("tenant-id-123", It.IsAny<IList<User>>(), "generatedPassword", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure<(int createdQry, string? errors)>(error));

        _licenseServiceMock.Setup(x => x.Consume(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());
        _licenseServiceMock.Setup(x => x.Refund(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());

        // Act
        var result = await _handler.Handle(command, It.IsAny<CancellationToken>());

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().BeEquivalentTo(error);

        _licenseServiceMock.Verify(x => x.Consume(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
        _licenseServiceMock.Verify(x => x.Refund(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Test]
    public async Task Handle_ForceChangePasswordFails_ReturnsFailure()
    {
        // Arrange: Flujo de usuario existente, pero falla el cambio de contraseña
        var command = new ExternalLoginCommand { LegacyJwt = "legacyJwtToken" };
        _tenantProviderMock.Setup(x => x.GetTenantName()).Returns("TenantName");
        _tenantProviderMock.Setup(x => x.GetTenantCountry()).Returns("TenantCountry");
        _identityServiceMock.Setup(x => x.GetTenantIdByNameAndCountry("TenantName", "TenantCountry", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("tenant-id-123"));
        _settingsServiceMock.Setup(x => x.GetJWKAsync("tenant-id-123", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("jwkJsonString"));

        var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Email, "<EMAIL>"),
            new Claim("roles", "[Standard]")
        };
        var claimsIdentity = new ClaimsIdentity(claims);
        _externalJwtValidatorMock.Setup(x => x.ValidateJwtByJwkAsync("jwkJsonString", "legacyJwtToken"))
            .ReturnsAsync(Result.Success(claimsIdentity));

        // Usuario existe
        _identityServiceMock.Setup(x => x.UserExists("tenant-id-123", "<EMAIL>", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(true));

        _passwordGeneratorMock.Setup(x => x.Generate()).Returns("generatedPassword");

        var error = new Error("FORCE_CHANGE_ERROR", "Force change password error");
        _identityServiceMock.Setup(x => x.ForceChangePassword("tenant-id-123", "<EMAIL>", "generatedPassword", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure<bool>(error));

        // Act
        var result = await _handler.Handle(command, It.IsAny<CancellationToken>());

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().BeEquivalentTo(error);
    }

    [Test]
    public async Task Handle_LoginFails_ReturnsFailure()
    {
        // Arrange: Flujo existente con fallo en el login
        var command = new ExternalLoginCommand { LegacyJwt = "legacyJwtToken" };
        _tenantProviderMock.Setup(x => x.GetTenantName()).Returns("TenantName");
        _tenantProviderMock.Setup(x => x.GetTenantCountry()).Returns("TenantCountry");
        _identityServiceMock.Setup(x => x.GetTenantIdByNameAndCountry("TenantName", "TenantCountry", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("tenant-id-123"));
        _settingsServiceMock.Setup(x => x.GetJWKAsync("tenant-id-123", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("jwkJsonString"));

        var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Email, "<EMAIL>"),
            new Claim("roles", "[Standard]")
        };
        var claimsIdentity = new ClaimsIdentity(claims);
        _externalJwtValidatorMock.Setup(x => x.ValidateJwtByJwkAsync("jwkJsonString", "legacyJwtToken"))
            .ReturnsAsync(Result.Success(claimsIdentity));

        // Usuario existente
        _identityServiceMock.Setup(x => x.UserExists("tenant-id-123", "<EMAIL>", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(true));

        _passwordGeneratorMock.Setup(x => x.Generate()).Returns("generatedPassword");

        _identityServiceMock.Setup(x => x.ForceChangePassword("tenant-id-123", "<EMAIL>", "generatedPassword", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(true));

        var error = new Error("LOGIN_ERROR", "Login error");
        _identityServiceMock.Setup(x => x.LoginAsync("tenant-id-123", "<EMAIL>", "generatedPassword", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure<LoginResponse>(error));

        // Act
        var result = await _handler.Handle(command, It.IsAny<CancellationToken>());

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().BeEquivalentTo(error);
    }

    [Test]
    public async Task Handle_InvalidChallenge_ReturnsFailure()
    {
        // Arrange: Login retorna un challenge distinto de "NEW_PASSWORD_REQUIRED"
        var command = new ExternalLoginCommand { LegacyJwt = "legacyJwtToken" };
        _tenantProviderMock.Setup(x => x.GetTenantName()).Returns("TenantName");
        _tenantProviderMock.Setup(x => x.GetTenantCountry()).Returns("TenantCountry");
        _identityServiceMock.Setup(x => x.GetTenantIdByNameAndCountry("TenantName", "TenantCountry", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("tenant-id-123"));
        _settingsServiceMock.Setup(x => x.GetJWKAsync("tenant-id-123", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("jwkJsonString"));

        var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Email, "<EMAIL>"),
            new Claim("roles", "[Standard]")
        };
        var claimsIdentity = new ClaimsIdentity(claims);
        _externalJwtValidatorMock.Setup(x => x.ValidateJwtByJwkAsync("jwkJsonString", "legacyJwtToken"))
            .ReturnsAsync(Result.Success(claimsIdentity));

        // Usuario existente
        _identityServiceMock.Setup(x => x.UserExists("tenant-id-123", "<EMAIL>", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(true));

        _passwordGeneratorMock.Setup(x => x.Generate()).Returns("generatedPassword");

        _identityServiceMock.Setup(x => x.ForceChangePassword("tenant-id-123", "<EMAIL>", "generatedPassword", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(true));

        // Login retorna un challenge con nombre inválido
        var loginResponse = new LoginResponse("OTHER_CHALLENGE", "sessionToken", "tenant-id-123", null, null);
        _identityServiceMock.Setup(x => x.LoginAsync("tenant-id-123", "<EMAIL>", "generatedPassword", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(loginResponse));

        // Act
        var result = await _handler.Handle(command, It.IsAny<CancellationToken>());

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("INVALID_CHALLENGE");
    }

    [Test]
    public async Task Handle_RespondToAuthChallengeFails_ReturnsFailure()
    {
        // Arrange: Flujo challenge donde falla la respuesta
        var command = new ExternalLoginCommand { LegacyJwt = "legacyJwtToken" };
        _tenantProviderMock.Setup(x => x.GetTenantName()).Returns("TenantName");
        _tenantProviderMock.Setup(x => x.GetTenantCountry()).Returns("TenantCountry");
        _identityServiceMock.Setup(x => x.GetTenantIdByNameAndCountry("TenantName", "TenantCountry", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("tenant-id-123"));
        _settingsServiceMock.Setup(x => x.GetJWKAsync("tenant-id-123", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("jwkJsonString"));

        var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Email, "<EMAIL>"),
            new Claim("roles", "[Standard]")
        };
        var claimsIdentity = new ClaimsIdentity(claims);
        _externalJwtValidatorMock.Setup(x => x.ValidateJwtByJwkAsync("jwkJsonString", "legacyJwtToken"))
            .ReturnsAsync(Result.Success(claimsIdentity));

        // Usuario existente
        _identityServiceMock.Setup(x => x.UserExists("tenant-id-123", "<EMAIL>", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(true));

        _passwordGeneratorMock.Setup(x => x.Generate()).Returns("generatedPassword");

        _identityServiceMock.Setup(x => x.ForceChangePassword("tenant-id-123", "<EMAIL>", "generatedPassword", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(true));

        // Login retorna challenge NEW_PASSWORD_REQUIRED
        var loginResponse = new LoginResponse("NEW_PASSWORD_REQUIRED", "sessionToken", "tenant-id-123", null, null);
        _identityServiceMock.Setup(x => x.LoginAsync("tenant-id-123", "<EMAIL>", "generatedPassword", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(loginResponse));

        var error = new Error("CHALLENGE_ERROR", "Challenge error");
        _identityServiceMock.Setup(x => x.RespondToAuthChallengeAsync(
            "tenant-id-123",
            "sessionToken",
            "<EMAIL>",
            "NEW_PASSWORD_REQUIRED",
            It.Is<Dictionary<string, string>>(d => d["NEW_PASSWORD"] == "generatedPassword"),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure<LoginResponse>(error));

        // Act
        var result = await _handler.Handle(command, It.IsAny<CancellationToken>());

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().BeEquivalentTo(error);
    }

    [Test]
    public async Task Handle_MissingRolesClaim_ReturnsFailure()
    {
        // Arrange
        var command = new ExternalLoginCommand { LegacyJwt = "legacyJwtToken" };
        _tenantProviderMock.Setup(x => x.GetTenantName()).Returns("TenantName");
        _tenantProviderMock.Setup(x => x.GetTenantCountry()).Returns("TenantCountry");

        _identityServiceMock
            .Setup(x => x.GetTenantIdByNameAndCountry("TenantName", "TenantCountry", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("tenant-id-123"));

        _identityServiceMock.Setup(x => x.UserExists("tenant-id-123", "<EMAIL>", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(false));

        _settingsServiceMock
            .Setup(x => x.GetJWKAsync("tenant-id-123", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("jwkJsonString"));

        // Se proveen solo el claim de email, sin claim "roles"
        var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Email, "<EMAIL>")
        };

        var claimsPrincipal = new ClaimsIdentity(claims);

        _externalJwtValidatorMock
            .Setup(x => x.ValidateJwtByJwkAsync("jwkJsonString", "legacyJwtToken"))
            .ReturnsAsync(Result.Success(claimsPrincipal));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("INVALID_TOKEN_CLAIMS");
        result.Error.Message.Should().Be("Invalid token claims");
    }

    [Test]
    public async Task Handle_EmptyRolesClaim_ReturnsFailure()
    {
        // Arrange
        var command = new ExternalLoginCommand { LegacyJwt = "legacyJwtToken" };
        _tenantProviderMock.Setup(x => x.GetTenantName()).Returns("TenantName");
        _tenantProviderMock.Setup(x => x.GetTenantCountry()).Returns("TenantCountry");

        _identityServiceMock
            .Setup(x => x.GetTenantIdByNameAndCountry("TenantName", "TenantCountry", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("tenant-id-123"));

        _identityServiceMock.Setup(x => x.UserExists("tenant-id-123", "<EMAIL>", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(false));

        _settingsServiceMock
            .Setup(x => x.GetJWKAsync("tenant-id-123", It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("jwkJsonString"));

        // Se proveen claims con email y el claim "roles" presente pero vacío
        var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Email, "<EMAIL>"),
            new Claim("roles", "")
        };
        var claimsPrincipal = new ClaimsIdentity(claims);
        _externalJwtValidatorMock
            .Setup(x => x.ValidateJwtByJwkAsync("jwkJsonString", "legacyJwtToken"))
            .ReturnsAsync(Result.Success(claimsPrincipal));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("INVALID_TOKEN_CLAIMS");
        result.Error.Message.Should().Be("Invalid token claims");
    }
}
