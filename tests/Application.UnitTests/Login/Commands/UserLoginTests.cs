﻿using N5.Result;
using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Application.Common.Models;
using UsersManagement.Application.Login.Commands.UserLogin;
using UsersManagement.Domain.DTO;

namespace UsersManagement.Application.UnitTests.Login.Commands;
public class UserLoginTests
{
    private Mock<IIdentityService> _identityServiceMock;
    private Mock<ILicenseService> _licenseServiceMock;
    private Mock<ITenantProvider> _tenantProviderMock;

    private const string USER_POOL_ID = "test_arg";

    [SetUp]
    public void Setup()
    {
        _identityServiceMock = new();
        _identityServiceMock
            .Setup(x => x.LoginAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure<LoginResponse>(Domain.Errors.IdentityErrors.Cognito.LOGIN_GENERIC));

        _identityServiceMock
            .Setup(x => x.GetTenantIdByNameAndCountry(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("test_tenant"));

        _licenseServiceMock = new();
        _licenseServiceMock
            .Setup(x => x.GetLicenseAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new License("test_tenant", 10, 5, DateTime.UtcNow.AddDays(-1)));

        _tenantProviderMock = new Mock<ITenantProvider>();

        _tenantProviderMock
            .Setup(x => x.GetTenantId())
            .Returns(USER_POOL_ID);
        _tenantProviderMock
            .Setup(x => x.GetTenantName())
            .Returns("TenantName");
        _tenantProviderMock
            .Setup(x => x.GetTenantCountry())
            .Returns("TenantCountry");
    }

    [Test]
    public async Task LoginWithExpiredLicense_ShouldReturnError()
    {
        var loginCommand = new UserLoginCommand()
        {
            Email = "<EMAIL>",
            Password = "test"
        };
        var commandHandler = new UserLoginCommandHandler(_identityServiceMock.Object, _licenseServiceMock.Object, _tenantProviderMock.Object);

        var result = await commandHandler.Handle(loginCommand, CancellationToken.None);

        result.Should().NotBeNull();

        result.IsFailure.Should().BeTrue();

        result.Error.Should().Be(Domain.Errors.LoginErrors.License.EXPIRED);
    }
}
