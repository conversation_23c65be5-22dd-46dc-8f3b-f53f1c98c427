using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.Extensions.Logging;
using N5.Result;
using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Application.Common.Models;
using UsersManagement.Application.Login.Commands.GenerateSession;
using UsersManagement.Domain.Entities;
using UsersManagement.Domain.Enums;

namespace UsersManagement.Application.UnitTests.Login.Commands;

public class GenerateSessionTests
{
    private Mock<ISessionService> _sessionServiceMock;
    private Mock<ITenantProvider> _tenantProviderMock;
    private Mock<IIdentityService> _identityServiceMock;
    private Mock<ISettingsService> _settingsServiceMock;
    private Mock<IExternalJwtValidator> _externalJwtValidatorMock;
    private Mock<IPasswordGenerator> _passwordGeneratorMock;
    private Mock<ILicenseService> _licenseServiceMock;
    private Mock<ILogger<GenerateSessionCommandHandler>> _loggerMock;
    private GenerateSessionCommandHandler _handler;

    private const string TenantName = "TestTenant";
    private const string TenantCountry = "TestCountry";
    private const string TenantId = "tenant-123";
    private const string SessionCode = "session-code-123";
    private const string Jwt = "test.jwt.token";
    private const string JwkValue = "{\"keys\":[{\"kty\":\"RSA\",\"kid\":\"test-key\"}]}";
    private const string Email = "<EMAIL>";
    private const string Password = "TestPassword123!";

    [SetUp]
    public void Setup()
    {
        _sessionServiceMock = new Mock<ISessionService>(MockBehavior.Strict);
        _tenantProviderMock = new Mock<ITenantProvider>(MockBehavior.Strict);
        _identityServiceMock = new Mock<IIdentityService>(MockBehavior.Strict);
        _settingsServiceMock = new Mock<ISettingsService>(MockBehavior.Strict);
        _externalJwtValidatorMock = new Mock<IExternalJwtValidator>(MockBehavior.Strict);
        _passwordGeneratorMock = new Mock<IPasswordGenerator>(MockBehavior.Strict);
        _licenseServiceMock = new Mock<ILicenseService>(MockBehavior.Strict);
        _loggerMock = new Mock<ILogger<GenerateSessionCommandHandler>>(MockBehavior.Loose);

        _tenantProviderMock.Setup(tp => tp.GetTenantName()).Returns(TenantName);
        _tenantProviderMock.Setup(tp => tp.GetTenantCountry()).Returns(TenantCountry);
        _passwordGeneratorMock.Setup(pg => pg.Generate()).Returns(Password);

        _handler = new GenerateSessionCommandHandler(
            _sessionServiceMock.Object,
            _tenantProviderMock.Object,
            _identityServiceMock.Object,
            _settingsServiceMock.Object,
            _externalJwtValidatorMock.Object,
            _passwordGeneratorMock.Object,
            _licenseServiceMock.Object,
            _loggerMock.Object);
    }

    [Test]
    public async Task Handle_GetTenantIdFails_ReturnsFailure()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var error = new Error("TENANT_ERROR", "Failed to get tenant ID");

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Failure<string>(error));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(error);
    }

    [Test]
    public async Task Handle_GetJwtFails_ReturnsFailure()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var error = new Error("JWT_ERROR", "Failed to get JWT");

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _sessionServiceMock
            .Setup(s => s.GetJwtAsync(TenantId, SessionCode, cancellationToken))
            .ReturnsAsync(Result.Failure<string>(error));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(error);
    }

    [Test]
    public async Task Handle_GetJwkFails_ReturnsFailure()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var error = new Error("JWK_ERROR", "Failed to get JWK");

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _sessionServiceMock
            .Setup(s => s.GetJwtAsync(TenantId, SessionCode, cancellationToken))
            .ReturnsAsync(Result.Success(Jwt));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Failure<string>(error));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(error);
    }
    
    [Test]
    public async Task Handle_ValidateJwtFails_ReturnsFailure()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var error = new Error("JWT_VALIDATION_ERROR", "Failed to validate JWT");

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _sessionServiceMock
            .Setup(s => s.GetJwtAsync(TenantId, SessionCode, cancellationToken))
            .ReturnsAsync(Result.Success(Jwt));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        _externalJwtValidatorMock
            .Setup(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt))
            .ReturnsAsync(Result.Failure<ClaimsIdentity>(error));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(error);
    }
    
    [Test]
    public async Task Handle_InvalidEmailClaim_ReturnsFailure()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var claimsIdentity = new ClaimsIdentity(new[] { new Claim("other-claim", "value") });

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _sessionServiceMock
            .Setup(s => s.GetJwtAsync(TenantId, SessionCode, cancellationToken))
            .ReturnsAsync(Result.Success(Jwt));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        _externalJwtValidatorMock
            .Setup(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt))
            .ReturnsAsync(Result.Success(claimsIdentity));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("INVALID_TOKEN_CLAIMS");
        result.Error.Message.Should().Be("Invalid token claims");
    }
    
    [Test]
    public async Task Handle_InvalidEmailFormat_ReturnsFailure()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var claimsIdentity = new ClaimsIdentity(new[] { new Claim(JwtRegisteredClaimNames.Email, "invalid-email") });

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _sessionServiceMock
            .Setup(s => s.GetJwtAsync(TenantId, SessionCode, cancellationToken))
            .ReturnsAsync(Result.Success(Jwt));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        _externalJwtValidatorMock
            .Setup(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt))
            .ReturnsAsync(Result.Success(claimsIdentity));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("INVALID_TOKEN_CLAIMS");
        result.Error.Message.Should().Be("Invalid token claims");
    }
    
    [Test]
    public async Task Handle_UserExistCheckFails_ReturnsFailure()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var error = new Error("USER_CHECK_ERROR", "Failed to check if user exists");
        var claimsIdentity = new ClaimsIdentity(new[] { new Claim(JwtRegisteredClaimNames.Email, Email) });

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _sessionServiceMock
            .Setup(s => s.GetJwtAsync(TenantId, SessionCode, cancellationToken))
            .ReturnsAsync(Result.Success(Jwt));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        _externalJwtValidatorMock
            .Setup(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt))
            .ReturnsAsync(Result.Success(claimsIdentity));

        _identityServiceMock
            .Setup(s => s.UserExists(TenantId, Email, cancellationToken))
            .ReturnsAsync(Result.Failure<bool>(error));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(error);
    }
    
    [Test]
    public async Task Handle_UserDoesNotExist_GetRolesFromClaimsFails_ReturnsFailure()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var claimsIdentity = new ClaimsIdentity(new[] { 
            new Claim(JwtRegisteredClaimNames.Email, Email),
            // Missing roles claim
        });

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _sessionServiceMock
            .Setup(s => s.GetJwtAsync(TenantId, SessionCode, cancellationToken))
            .ReturnsAsync(Result.Success(Jwt));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        _externalJwtValidatorMock
            .Setup(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt))
            .ReturnsAsync(Result.Success(claimsIdentity));

        _identityServiceMock
            .Setup(s => s.UserExists(TenantId, Email, cancellationToken))
            .ReturnsAsync(Result.Success(false)); // User does not exist

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("INVALID_TOKEN_CLAIMS");
        result.Error.Message.Should().Be("Invalid token claims");
    }
    
    [Test]
    public async Task Handle_UserDoesNotExist_ConsumeLicenseFails_ReturnsFailure()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var error = new Error("LICENSE_ERROR", "Failed to consume license");
        var claimsIdentity = new ClaimsIdentity(new[] { 
            new Claim(JwtRegisteredClaimNames.Email, Email),
            new Claim("roles", "[Standard]") 
        });

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _sessionServiceMock
            .Setup(s => s.GetJwtAsync(TenantId, SessionCode, cancellationToken))
            .ReturnsAsync(Result.Success(Jwt));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        _externalJwtValidatorMock
            .Setup(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt))
            .ReturnsAsync(Result.Success(claimsIdentity));

        _identityServiceMock
            .Setup(s => s.UserExists(TenantId, Email, cancellationToken))
            .ReturnsAsync(Result.Success(false)); // User does not exist

        _licenseServiceMock
            .Setup(s => s.Consume(TenantId, cancellationToken))
            .ReturnsAsync(Result.Failure(error));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("CONSUME_CREDIT_FAILED");
        result.Error.Message.Should().Be("Credits consumption failed");
    }
    
    [Test]
    public async Task Handle_UserDoesNotExist_CreateUserFails_RefundsLicense_ReturnsFailure()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var error = new Error("CREATE_USER_ERROR", "Failed to create user");
        var claimsIdentity = new ClaimsIdentity(new[] { 
            new Claim(JwtRegisteredClaimNames.Email, Email),
            new Claim("roles", "[Standard]") 
        });

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _sessionServiceMock
            .Setup(s => s.GetJwtAsync(TenantId, SessionCode, cancellationToken))
            .ReturnsAsync(Result.Success(Jwt));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        _externalJwtValidatorMock
            .Setup(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt))
            .ReturnsAsync(Result.Success(claimsIdentity));

        _identityServiceMock
            .Setup(s => s.UserExists(TenantId, Email, cancellationToken))
            .ReturnsAsync(Result.Success(false)); // User does not exist

        _licenseServiceMock
            .Setup(s => s.Consume(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success());
            
        _identityServiceMock
            .Setup(s => s.CreateUsersAsync(TenantId, It.IsAny<IList<User>>(), Password, cancellationToken))
            .ReturnsAsync(Result.Failure<(int, string?)>(error));
            
        _licenseServiceMock
            .Setup(s => s.Refund(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success());

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(error);
        
        // Verify license was refunded
        _licenseServiceMock.Verify(s => s.Refund(TenantId, cancellationToken), Times.Once);
    }
    
    [Test]
    public async Task Handle_UserDoesNotExist_CreateUserThrowsException_RefundsLicense_ReturnsFailure()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var exception = new Exception("Test exception");
        var claimsIdentity = new ClaimsIdentity(new[] { 
            new Claim(JwtRegisteredClaimNames.Email, Email),
            new Claim("roles", "[Standard]") 
        });

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _sessionServiceMock
            .Setup(s => s.GetJwtAsync(TenantId, SessionCode, cancellationToken))
            .ReturnsAsync(Result.Success(Jwt));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        _externalJwtValidatorMock
            .Setup(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt))
            .ReturnsAsync(Result.Success(claimsIdentity));

        _identityServiceMock
            .Setup(s => s.UserExists(TenantId, Email, cancellationToken))
            .ReturnsAsync(Result.Success(false)); // User does not exist

        _licenseServiceMock
            .Setup(s => s.Consume(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success());
            
        _identityServiceMock
            .Setup(s => s.CreateUsersAsync(TenantId, It.IsAny<IList<User>>(), Password, cancellationToken))
            .ThrowsAsync(exception);
            
        _licenseServiceMock
            .Setup(s => s.Refund(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success());

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("UNHANDLED_CREATING_USER");
        result.Error.Message.Should().Be(exception.Message);

        // Verify logger was called with exception
        _loggerMock.Verify(x => x.Log(
             It.IsAny<LogLevel>(),
             It.IsAny<EventId>(),
             It.IsAny<It.IsAnyType>(),
             It.IsAny<Exception>(),
             (Func<It.IsAnyType, Exception?, string>)It.IsAny<object>()), Times.Once);

        // Verify license was refunded
        _licenseServiceMock.Verify(s => s.Refund(TenantId, cancellationToken), Times.Once);
    }
    
    [Test]
    public async Task Handle_UserDoesNotExist_CreateUserSucceeds_LoginSucceeds_ReturnsSuccess()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var loginResponse = new LoginResponse(null, null, TenantId, "access-token", "refresh-token");
        var claimsIdentity = new ClaimsIdentity(
        [ 
            new Claim(JwtRegisteredClaimNames.Email, Email),
            new Claim("roles", "[Standard]") 
        ]);

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _sessionServiceMock
            .Setup(s => s.GetJwtAsync(TenantId, SessionCode, cancellationToken))
            .ReturnsAsync(Result.Success(Jwt));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        _externalJwtValidatorMock
            .Setup(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt))
            .ReturnsAsync(Result.Success(claimsIdentity));

        _identityServiceMock
            .Setup(s => s.UserExists(TenantId, Email, cancellationToken))
            .ReturnsAsync(Result.Success(false)); // User does not exist

        _licenseServiceMock
            .Setup(s => s.Consume(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success());
            
        _identityServiceMock
            .Setup(s => s.CreateUsersAsync(TenantId, It.IsAny<IList<User>>(), Password, cancellationToken))
            .ReturnsAsync(Result.Success((1, (string?)null)));
            
        _identityServiceMock
            .Setup(s => s.LoginAsync(TenantId, Email, Password, cancellationToken))
            .ReturnsAsync(Result.Success(loginResponse));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(loginResponse);
    }
    
    [Test]
    public async Task Handle_UserExists_ChangePasswordFails_ReturnsFailure()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var error = new Error("PASSWORD_ERROR", "Failed to change password");
        var claimsIdentity = new ClaimsIdentity(new[] { 
            new Claim(JwtRegisteredClaimNames.Email, Email)
        });

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _sessionServiceMock
            .Setup(s => s.GetJwtAsync(TenantId, SessionCode, cancellationToken))
            .ReturnsAsync(Result.Success(Jwt));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        _externalJwtValidatorMock
            .Setup(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt))
            .ReturnsAsync(Result.Success(claimsIdentity));

        _identityServiceMock
            .Setup(s => s.UserExists(TenantId, Email, cancellationToken))
            .ReturnsAsync(Result.Success(true)); // User exists
            
        _identityServiceMock
            .Setup(s => s.ForceChangePassword(TenantId, Email, Password, cancellationToken))
            .ReturnsAsync(Result.Failure(error));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(error);
    }
    
    [Test]
    public async Task Handle_UserExists_ChangePasswordSucceeds_LoginFails_ReturnsFailure()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var error = new Error("LOGIN_ERROR", "Failed to login");
        var claimsIdentity = new ClaimsIdentity(new[] { 
            new Claim(JwtRegisteredClaimNames.Email, Email)
        });

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _sessionServiceMock
            .Setup(s => s.GetJwtAsync(TenantId, SessionCode, cancellationToken))
            .ReturnsAsync(Result.Success(Jwt));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        _externalJwtValidatorMock
            .Setup(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt))
            .ReturnsAsync(Result.Success(claimsIdentity));

        _identityServiceMock
            .Setup(s => s.UserExists(TenantId, Email, cancellationToken))
            .ReturnsAsync(Result.Success(true)); // User exists
            
        _identityServiceMock
            .Setup(s => s.ForceChangePassword(TenantId, Email, Password, cancellationToken))
            .ReturnsAsync(Result.Success());
            
        _identityServiceMock
            .Setup(s => s.LoginAsync(TenantId, Email, Password, cancellationToken))
            .ReturnsAsync(Result.Failure<LoginResponse>(error));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(error);
    }
    
    [Test]
    public async Task Handle_UserExists_ChangePasswordSucceeds_LoginSucceeds_NoChallenge_ReturnsSuccess()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var loginResponse = new LoginResponse(null, null, TenantId, "access-token", "refresh-token");
        var claimsIdentity = new ClaimsIdentity(
        [
            new Claim(JwtRegisteredClaimNames.Email, Email)
        ]);

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _sessionServiceMock
            .Setup(s => s.GetJwtAsync(TenantId, SessionCode, cancellationToken))
            .ReturnsAsync(Result.Success(Jwt));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        _externalJwtValidatorMock
            .Setup(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt))
            .ReturnsAsync(Result.Success(claimsIdentity));

        _identityServiceMock
            .Setup(s => s.UserExists(TenantId, Email, cancellationToken))
            .ReturnsAsync(Result.Success(true)); // User exists
            
        _identityServiceMock
            .Setup(s => s.ForceChangePassword(TenantId, Email, Password, cancellationToken))
            .ReturnsAsync(Result.Success());
            
        _identityServiceMock
            .Setup(s => s.LoginAsync(TenantId, Email, Password, cancellationToken))
            .ReturnsAsync(Result.Success(loginResponse));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(loginResponse);
    }
    
    [Test]
    public async Task Handle_UserExists_ChangePasswordSucceeds_LoginSucceeds_InvalidChallenge_ReturnsFailure()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var loginResponse = new LoginResponse("INVALID_CHALLENGE", "session-id", TenantId, null, null);
        var claimsIdentity = new ClaimsIdentity(
        [
            new Claim(JwtRegisteredClaimNames.Email, Email)
        ]);

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _sessionServiceMock
            .Setup(s => s.GetJwtAsync(TenantId, SessionCode, cancellationToken))
            .ReturnsAsync(Result.Success(Jwt));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        _externalJwtValidatorMock
            .Setup(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt))
            .ReturnsAsync(Result.Success(claimsIdentity));

        _identityServiceMock
            .Setup(s => s.UserExists(TenantId, Email, cancellationToken))
            .ReturnsAsync(Result.Success(true)); // User exists
            
        _identityServiceMock
            .Setup(s => s.ForceChangePassword(TenantId, Email, Password, cancellationToken))
            .ReturnsAsync(Result.Success());
            
        _identityServiceMock
            .Setup(s => s.LoginAsync(TenantId, Email, Password, cancellationToken))
            .ReturnsAsync(Result.Success(loginResponse));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("INVALID_CHALLENGE");
        result.Error.Message.Should().Be("Not recognized challenge");
    }
    
    [Test]
    public async Task Handle_UserExists_ChangePasswordSucceeds_LoginSucceeds_ValidChallenge_RespondChallengeFails_ReturnsFailure()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var loginResponse = new LoginResponse("NEW_PASSWORD_REQUIRED", "session-id", TenantId, null, null);
        var error = new Error("CHALLENGE_ERROR", "Failed to respond to challenge");
        var claimsIdentity = new ClaimsIdentity(
        [ 
            new Claim(JwtRegisteredClaimNames.Email, Email)
        ]);

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _sessionServiceMock
            .Setup(s => s.GetJwtAsync(TenantId, SessionCode, cancellationToken))
            .ReturnsAsync(Result.Success(Jwt));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        _externalJwtValidatorMock
            .Setup(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt))
            .ReturnsAsync(Result.Success(claimsIdentity));

        _identityServiceMock
            .Setup(s => s.UserExists(TenantId, Email, cancellationToken))
            .ReturnsAsync(Result.Success(true)); // User exists
            
        _identityServiceMock
            .Setup(s => s.ForceChangePassword(TenantId, Email, Password, cancellationToken))
            .ReturnsAsync(Result.Success());
            
        _identityServiceMock
            .Setup(s => s.LoginAsync(TenantId, Email, Password, cancellationToken))
            .ReturnsAsync(Result.Success(loginResponse));
            
        _identityServiceMock
            .Setup(s => s.RespondToAuthChallengeAsync(
                TenantId, 
                loginResponse.Session!, 
                Email, 
                loginResponse.ChallengeName!, 
                It.Is<Dictionary<string, string>>(d => d.ContainsKey("NEW_PASSWORD") && d["NEW_PASSWORD"] == Password),
                cancellationToken))
            .ReturnsAsync(Result.Failure<LoginResponse>(error));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(error);
    }
    
    [Test]
    public async Task Handle_UserExists_ChangePasswordSucceeds_LoginSucceeds_ValidChallenge_RespondChallengeSucceeds_ReturnsSuccess()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var loginResponse = new LoginResponse("NEW_PASSWORD_REQUIRED", "session-id", TenantId, null, null);
        var challengeResponse = new LoginResponse(null, null, TenantId, "challenge-access-token", "challenge-id-token");
        var claimsIdentity = new ClaimsIdentity(
        [ 
            new Claim(JwtRegisteredClaimNames.Email, Email)
        ]);

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _sessionServiceMock
            .Setup(s => s.GetJwtAsync(TenantId, SessionCode, cancellationToken))
            .ReturnsAsync(Result.Success(Jwt));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        _externalJwtValidatorMock
            .Setup(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt))
            .ReturnsAsync(Result.Success(claimsIdentity));

        _identityServiceMock
            .Setup(s => s.UserExists(TenantId, Email, cancellationToken))
            .ReturnsAsync(Result.Success(true)); // User exists
            
        _identityServiceMock
            .Setup(s => s.ForceChangePassword(TenantId, Email, Password, cancellationToken))
            .ReturnsAsync(Result.Success());
            
        _identityServiceMock
            .Setup(s => s.LoginAsync(TenantId, Email, Password, cancellationToken))
            .ReturnsAsync(Result.Success(loginResponse));
            
        _identityServiceMock
            .Setup(s => s.RespondToAuthChallengeAsync(
                TenantId, 
                loginResponse.Session!, 
                Email, 
                loginResponse.ChallengeName!, 
                It.Is<Dictionary<string, string>>(d => d.ContainsKey("NEW_PASSWORD") && d["NEW_PASSWORD"] == Password),
                cancellationToken))
            .ReturnsAsync(Result.Success(challengeResponse));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(challengeResponse);
    }
    
    [Test]
    public async Task Handle_ThrowsException_ReturnsFailure()
    {
        // Arrange
        var command = new GenerateSessionCommand { SessionCode = SessionCode };
        var cancellationToken = CancellationToken.None;
        var exception = new Exception("Test exception");

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ThrowsAsync(exception);

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("JWT_TRANSFORMATION_FAILED");
        result.Error.Message.Should().Be("Error trying to convert JWT");

        // Verify logger was called with exception
        _loggerMock.Verify(x => x.Log(
            It.IsAny<LogLevel>(),
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            (Func<It.IsAnyType, Exception?, string>)It.IsAny<object>()), Times.Once);
    }
}
