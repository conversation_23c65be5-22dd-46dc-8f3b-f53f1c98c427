﻿using Microsoft.IdentityModel.Tokens;
using System.Text;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.Extensions.Logging;
using UsersManagement.Application.Common.Security;

namespace UsersManagement.Application.UnitTests.Login.Commands;
public class ExternalJwtValidatorTests
{
    private Mock<ILogger<ExternalJwtValidator>> _loggerMock;
    private ExternalJwtValidator _validator;

    [SetUp]
    public void Setup()
    {
        _loggerMock = new Mock<ILogger<ExternalJwtValidator>>();
        _validator = new ExternalJwtValidator(_loggerMock.Object);
    }

    private (string jwkJson, SymmetricSecurityKey securityKey) CreateJwkJsonAndKey(string secret)
    {
        var keyBytes = Encoding.UTF8.GetBytes(secret);
        var symmetricKey = new SymmetricSecurityKey(keyBytes);
        // Convierte la llave simétrica en un JsonWebKey
        var jsonWebKey = JsonWebKeyConverter.ConvertFromSymmetricSecurityKey(symmetricKey);
        // Construye el JSON representando un conjunto de llaves (JWK Set)
        var jwkJson = $"{{\"keys\":[{System.Text.Json.JsonSerializer.Serialize(jsonWebKey)}]}}";
        return (jwkJson, symmetricKey);
    }

    private string CreateToken(SymmetricSecurityKey key, DateTime expires, DateTime? notBefore = null, DateTime? issuedAt = null)
    {
        var signingCredentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
        var tokenHandler = new JwtSecurityTokenHandler();
        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(new[]
            {
                new Claim("sub", "123")
            }),
            IssuedAt = issuedAt,
            NotBefore = notBefore,
            Expires = expires,
            SigningCredentials = signingCredentials
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    [Test]
    public async Task ValidateJwtByJwk_ValidToken_ShouldReturnSuccess()
    {
        // Arrange
        string secret = "mysupersecretmysupersecretmysupersecret"; // Debe tener la longitud requerida
        var (jwkJson, securityKey) = CreateJwkJsonAndKey(secret);
        // Genera un token con fecha de expiración futura
        var token = CreateToken(securityKey, DateTime.UtcNow.AddHours(1));

        // Act
        var result = await _validator.ValidateJwtByJwkAsync(jwkJson, token);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().NotBeNull();
        result.Value.Claims.Should().Contain(c => c.Type == JwtRegisteredClaimNames.Sub && c.Value == "123");
    }

    [Test]
    public async Task ValidateJwtByJwk_InvalidToken_ShouldReturnFailure()
    {
        // Arrange
        string secret = "mysupersecretmysupersecretmysupersecret";
        var (jwkJson, _) = CreateJwkJsonAndKey(secret);
        string invalidToken = "thisisnotavalidtoken";

        // Act
        var result = await _validator.ValidateJwtByJwkAsync(jwkJson, invalidToken);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Error.Code.Should().Be("INVALID_TOKEN");
        result.Error.Message.Should().Be("Invalid legacy token");
    }

    [Test]
    public async Task ValidateJwtByJwk_NoKeyInJwkJson_ShouldReturnFailure()
    {
        // Arrange: se pasa un JWK JSON sin claves
        var jwkJson = "{\"keys\":[]}";
        string secret = "mysupersecretmysupersecretmysupersecret";
        // Se genera un token firmado con una llave, aunque no coincidirá con el JWK JSON
        var symmetricKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secret));
        var token = CreateToken(symmetricKey, DateTime.UtcNow.AddHours(1));

        // Act
        var result = await _validator.ValidateJwtByJwkAsync(jwkJson, token);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Error.Code.Should().Be("JWT_VALIDATION_FAILED");
        result.Error.Message.Should().Be("Error trying to validate JWT");

        _loggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Test]
    public async Task ValidateJwtByJwk_ExpiredToken_ShouldReturnFailure()
    {
        // Arrange
        string secret = "mysupersecretmysupersecretmysupersecret";
        var (jwkJson, securityKey) = CreateJwkJsonAndKey(secret);
        // Genera un token con fecha de expiración en el pasado
        var expiration = DateTime.UtcNow.AddMinutes(-10);
        var issued = DateTime.UtcNow.AddHours(-1);
        var notBefore = issued;
        var token = CreateToken(securityKey, expiration, notBefore, issued);

        // Act
        var result = await _validator.ValidateJwtByJwkAsync(jwkJson, token);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("INVALID_TOKEN");
        result.Error.Message.Should().Be("Invalid legacy token");
    }
}
