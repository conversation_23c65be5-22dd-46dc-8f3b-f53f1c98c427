using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.Extensions.Logging;
using N5.Result;
using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Application.Common.Models;
using UsersManagement.Application.Common.Security;
using UsersManagement.Application.Login.Commands.StoreJwt;

namespace UsersManagement.Application.UnitTests.Login.Commands;

public class StoreJwtTests
{
    private Mock<ISessionService> _sessionServiceMock;
    private Mock<ITenantProvider> _tenantProviderMock;
    private Mock<IIdentityService> _identityServiceMock;
    private Mock<ISettingsService> _settingsServiceMock;
    private Mock<IExternalJwtValidator> _externalJwtValidatorMock;
    private Mock<ILogger<StoreJwtCommand>> _loggerMock;
    private StoreJwtCommandHandler _handler;

    private const string TenantName = "TestTenant";
    private const string TenantCountry = "TestCountry";
    private const string TenantId = "tenant-123";
    private const string Jwt = "test.jwt.token";
    private const string JwkValue = "{\"keys\":[{\"kty\":\"RSA\",\"kid\":\"test-key\"}]}";
    private const string SessionKey = "session-key-123";

    [SetUp]
    public void Setup()
    {
        _sessionServiceMock = new Mock<ISessionService>(MockBehavior.Strict);
        _tenantProviderMock = new Mock<ITenantProvider>(MockBehavior.Strict);
        _identityServiceMock = new Mock<IIdentityService>(MockBehavior.Strict);
        _settingsServiceMock = new Mock<ISettingsService>(MockBehavior.Strict);
        _externalJwtValidatorMock = new Mock<IExternalJwtValidator>(MockBehavior.Strict);
        _loggerMock = new Mock<ILogger<StoreJwtCommand>>(MockBehavior.Loose);

        _tenantProviderMock.Setup(tp => tp.GetTenantName()).Returns(TenantName);
        _tenantProviderMock.Setup(tp => tp.GetTenantCountry()).Returns(TenantCountry);

        _handler = new StoreJwtCommandHandler(
            _sessionServiceMock.Object,
            _tenantProviderMock.Object,
            _identityServiceMock.Object,
            _settingsServiceMock.Object,
            _externalJwtValidatorMock.Object,
            _loggerMock.Object);
    }

    [Test]
    public async Task Handle_Success_ReturnsStoreJwtResponse()
    {
        // Arrange
        var command = new StoreJwtCommand(Jwt);
        var cancellationToken = CancellationToken.None;

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        var claimsIdentity = new ClaimsIdentity(new[] { new Claim(JwtRegisteredClaimNames.Email, "<EMAIL>") });
        _externalJwtValidatorMock
            .Setup(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt))
            .ReturnsAsync(Result.Success(claimsIdentity));

        _sessionServiceMock
            .Setup(s => s.StoreJwtAsync(TenantId, Jwt, cancellationToken))
            .ReturnsAsync(Result.Success(SessionKey));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Key.Should().Be(SessionKey);

        // Verify all mocks were called
        _tenantProviderMock.Verify(tp => tp.GetTenantName(), Times.Once);
        _tenantProviderMock.Verify(tp => tp.GetTenantCountry(), Times.Once);
        _identityServiceMock.Verify(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken), Times.Once);
        _settingsServiceMock.Verify(s => s.GetJWKAsync(TenantId, cancellationToken), Times.Once);
        _externalJwtValidatorMock.Verify(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt), Times.Once);
        _sessionServiceMock.Verify(s => s.StoreJwtAsync(TenantId, Jwt, cancellationToken), Times.Once);
    }

    [Test]
    public async Task Handle_GetTenantIdFails_ReturnsFailure()
    {
        // Arrange
        var command = new StoreJwtCommand(Jwt);
        var cancellationToken = CancellationToken.None;
        var error = new Error("TENANT_ERROR", "Failed to get tenant ID");

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Failure<string>(error));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(error);

        // Verify only necessary mocks were called
        _tenantProviderMock.Verify(tp => tp.GetTenantName(), Times.Once);
        _tenantProviderMock.Verify(tp => tp.GetTenantCountry(), Times.Once);
        _identityServiceMock.Verify(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken), Times.Once);
        _settingsServiceMock.Verify(s => s.GetJWKAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Test]
    public async Task Handle_GetJwkFails_ReturnsFailure()
    {
        // Arrange
        var command = new StoreJwtCommand(Jwt);
        var cancellationToken = CancellationToken.None;
        var error = new Error("JWK_ERROR", "Failed to get JWK");

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Failure<string>(error));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(error);

        // Verify only necessary mocks were called
        _settingsServiceMock.Verify(s => s.GetJWKAsync(TenantId, cancellationToken), Times.Once);
        _externalJwtValidatorMock.Verify(v => v.ValidateJwtByJwkAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
    }

    [Test]
    public async Task Handle_ValidateJwtFails_ReturnsFailure()
    {
        // Arrange
        var command = new StoreJwtCommand(Jwt);
        var cancellationToken = CancellationToken.None;
        var error = new Error("JWT_VALIDATION_ERROR", "Failed to validate JWT");

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        _externalJwtValidatorMock
            .Setup(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt))
            .ReturnsAsync(Result.Failure<ClaimsIdentity>(error));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(error);

        // Verify only necessary mocks were called
        _externalJwtValidatorMock.Verify(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt), Times.Once);
        _sessionServiceMock.Verify(s => s.StoreJwtAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Test]
    public async Task Handle_StoreJwtFails_ReturnsFailure()
    {
        // Arrange
        var command = new StoreJwtCommand(Jwt);
        var cancellationToken = CancellationToken.None;
        var error = new Error("STORE_JWT_ERROR", "Failed to store JWT");

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ReturnsAsync(Result.Success(TenantId));

        _settingsServiceMock
            .Setup(s => s.GetJWKAsync(TenantId, cancellationToken))
            .ReturnsAsync(Result.Success(JwkValue));

        var claimsIdentity = new ClaimsIdentity(new[] { new Claim(JwtRegisteredClaimNames.Email, "<EMAIL>") });
        _externalJwtValidatorMock
            .Setup(v => v.ValidateJwtByJwkAsync(JwkValue, Jwt))
            .ReturnsAsync(Result.Success(claimsIdentity));

        _sessionServiceMock
            .Setup(s => s.StoreJwtAsync(TenantId, Jwt, cancellationToken))
            .ReturnsAsync(Result.Failure<string>(error));

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(error);

        // Verify logger was called
        _loggerMock.Verify(x => x.Log(
            It.IsAny<LogLevel>(),
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            (Func<It.IsAnyType, Exception?, string>)It.IsAny<object>()), Times.Once);
    }
    
    [Test]
    public async Task Handle_ThrowsException_ReturnsFailure()
    {
        // Arrange
        var command = new StoreJwtCommand(Jwt);
        var cancellationToken = CancellationToken.None;
        var exception = new Exception("Test exception");

        _identityServiceMock
            .Setup(s => s.GetTenantIdByNameAndCountry(TenantName, TenantCountry, cancellationToken))
            .ThrowsAsync(exception);

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("JWT_STORAGE_FAILED");
        result.Error.Message.Should().Be("Error trying to store JWT");

        // Verify logger was called with exception
        _loggerMock.Verify(x => x.Log(
            It.IsAny<LogLevel>(),
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            (Func<It.IsAnyType, Exception?, string>)It.IsAny<object>()), Times.Once);
    }
}
