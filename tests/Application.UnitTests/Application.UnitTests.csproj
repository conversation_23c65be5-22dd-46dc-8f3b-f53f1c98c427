﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <RootNamespace>UsersManagement.Application.UnitTests</RootNamespace>
        <AssemblyName>UsersManagement.Application.UnitTests</AssemblyName>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.NET.Test.Sdk" />
        <PackageReference Include="nunit" />
        <PackageReference Include="NUnit.Analyzers">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="NUnit3TestAdapter" />
        <PackageReference Include="coverlet.collector">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="FluentAssertions" />
        <PackageReference Include="Moq" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\src\UsersManagement.Application\UsersManagement.Application.csproj" />
        <ProjectReference Include="..\..\src\UsersManagement.Infrastructure\UsersManagement.Infrastructure.csproj" />
    </ItemGroup>

</Project>
