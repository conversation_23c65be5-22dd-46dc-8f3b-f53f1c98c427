﻿using System.Net;
using Microsoft.Extensions.Logging;
using Moq;
using UsersManagement.Infrastructure.DynamoDB.Abstractions;
using UsersManagement.Infrastructure.Licenses;

namespace UsersManagement.Infrastructure.UnitTests.LicenseService;

public class LicenseServiceTests
{
    private Mock<ILicenseApi> _licenseApiMock;
    private Mock<ILogger<Licenses.LicenseService>> _licenseLoggerMock;
    private Licenses.LicenseService _licenseService;
    
    private const string USER_POOL_ID = "test_arg";

    [SetUp]
    public void Setup()
    {
        _licenseApiMock = new();
        _licenseLoggerMock = new();
        _licenseService = new Licenses.LicenseService(_licenseApiMock.Object, _licenseLoggerMock.Object);
    }

    [Test]
    public async Task GetLicenseAsync_ShouldReturnLicense_WhenDataIsValid()
    {
        // Arrange
        var tenantId = "tenant123";
        var licenseData = new Dictionary<string, string>
        {
            { "total", "10" },
            { "used", "5" },
            { "expiration_date", DateTime.UtcNow.AddMonths(1).ToString("s") }
        };

        _licenseApiMock
            .Setup(x => x.Get(It.IsAny<IDictionary<string, string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Domain.DTO.License(tenantId, 10, 5, DateTime.UtcNow.AddMonths(1)));

        // Act
        var result = await _licenseService.GetLicenseAsync(tenantId, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.TenantId.Should().Be(tenantId);
        result.Value.Total.Should().Be(10);
        result.Value.Used.Should().Be(5);
        result.Value.Free.Should().Be(result.Value.Total - result.Value.Used);
        result.Value.ExpirationDate.Should().BeCloseTo(DateTime.UtcNow.AddMonths(1), TimeSpan.FromSeconds(1));
    }

    [Test]
    public async Task GetLicenseAsync_ShouldReturnFailure_WhenResponseIsNotSuccessful()
    {
        // Arrange
        var tenantId = "tenant123";

        _licenseApiMock
            .Setup(api => api.Get(It.IsAny<IDictionary<string, string>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        // Act
        var result = await _licenseService.GetLicenseAsync(tenantId, CancellationToken.None);

        // Assert
        result.Should().BeEquivalentTo(
            Result.Failure(new Error("LICENCE_GET_FAILED", "Retrieving license fail.")));
    }

    [Test]
    public async Task ConsumeCredit_ReturnsSuccess_WhenResponseIsSuccessful()
    {
        // Arrange
        var tenantId = "tenant123";
        _licenseApiMock
            .Setup(api => api.Consume(It.IsAny<IDictionary<string, string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Refit.ApiResponse<object>(new HttpResponseMessage(HttpStatusCode.OK), null, new(), null));

        // Act
        var result = await _licenseService.Consume(tenantId, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Test]
    public async Task ConsumeCredit_ReturnsFailure_WhenResponseIsNotSuccessful()
    {
        // Arrange
        var tenantId = "tenant123";
        _licenseApiMock
            .Setup(api => api.Consume(It.IsAny<IDictionary<string, string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Refit.ApiResponse<object>(new HttpResponseMessage(HttpStatusCode.BadRequest), null, new(), null));

        // Act
        var result = await _licenseService.Consume(tenantId, CancellationToken.None);

        // Assert
        result.Should().BeEquivalentTo(
            Result.Failure(new Error("LICENSE_CONSUME_FAILED", "License consume failed")));
    }

    [Test]
    public async Task ConsumeCredit_ReturnsFailure_WhenExceptionIsThrown()
    {
        // Arrange
        var tenantId = "tenant123";
        _licenseApiMock
            .Setup(api => api.Consume(It.IsAny<IDictionary<string, string>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        // Act
        var result = await _licenseService.Consume(tenantId, CancellationToken.None);

        // Assert
        result.Should().BeEquivalentTo(
            Result.Failure(new Error("LICENSE_CONSUME_FAILED", "License consume failed")));

        // Verificamos que se haya logueado el error
        _licenseLoggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Test]
    public async Task Refund_ReturnsSuccess_WhenResponseIsSuccessful()
    {
        // Arrange
        var tenantId = "tenant123";
        _licenseApiMock
            .Setup(api => api.Refund(It.IsAny<IDictionary<string, string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Refit.ApiResponse<object>(new HttpResponseMessage(HttpStatusCode.OK), null, new(), null));

        // Act
        var result = await _licenseService.Refund(tenantId, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Test]
    public async Task Refund_ReturnsFailure_WhenResponseIsNotSuccessful()
    {
        // Arrange
        var tenantId = "tenant123";
        var cancellationToken = CancellationToken.None;
        var failedResponse = new HttpResponseMessage(HttpStatusCode.BadRequest);
        _licenseApiMock
            .Setup(api => api.Refund(It.IsAny<IDictionary<string, string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Refit.ApiResponse<object>(new HttpResponseMessage(HttpStatusCode.BadRequest), null, new(), null));

        // Act
        var result = await _licenseService.Refund(tenantId, cancellationToken);

        // Assert
        result.Should().BeEquivalentTo(
            Result.Failure(new Error("LICENSE_REFUND_FAILED", "License refund failed")));
    }

    [Test]
    public async Task Refund_ReturnsFailure_WhenExceptionIsThrown()
    {
        // Arrange
        var tenantId = "tenant123";
        var cancellationToken = CancellationToken.None;
        _licenseApiMock
            .Setup(api => api.Consume(It.IsAny<IDictionary<string, string>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        // Act
        var result = await _licenseService.Refund(tenantId, cancellationToken);

        // Assert
        result.Should().BeEquivalentTo(
            Result.Failure(new Error("LICENSE_REFUND_FAILED", "License refund failed")));

        // Verificamos que se haya logueado el error
        _licenseLoggerMock.Verify(
            x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }
}
