﻿using Moq;
using UsersManagement.Infrastructure.DynamoDB.Abstractions;
using UsersManagement.Infrastructure.Permissions;

namespace UsersManagement.Infrastructure.UnitTests.Permissions;

public class PermissionsServiceTests
{
    private Mock<IDynamoDB> _dynamoDbMock;
    private PermissionsService _service;
    private const string TenantId = "tenant123";

    [SetUp]
    public void SetUp()
    {
        _dynamoDbMock = new();
        _service = new(_dynamoDbMock.Object);
    }

    [Test]
    public async Task ExistsPermissions_AllPermissionsExist_ReturnsTrue()
    {
        var permissions = new List<string> { "perm1", "perm2" };
        // Setup GetMultipleItems to return one item for each permission
        foreach (var perm in permissions)
        {
            _dynamoDbMock
                .Setup(db => db.GetMultipleItems(
                    It.IsAny<string>(),
                    It.Is<Dictionary<string, string>>(d =>
                            d.Contains<PERSON>ey("tenant_id") && d["tenant_id"] == TenantId &&
                            d.Contains<PERSON>ey("value") && d["value"] == perm),
                    It.IsAny<Dictionary<string, object>>(),
                    It.IsAny<int>(),It.IsAny<string?>(),
                    CancellationToken.None))
                .ReturnsAsync(Result.Success(new List<Dictionary<string, string>>
                {
                        new Dictionary<string, string>
                        {
                            { "value", perm }
                        }
                }));
        }

        var result = await _service.ExistsPermissions(TenantId, permissions, CancellationToken.None);

        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeTrue();
    }

    [Test]
    public async Task ExistsPermissions_NotAllPermissionsExist_ReturnsFalse()
    {
        var permissions = new List<string> { "perm1", "perm2" };
        // First permission exists
        _dynamoDbMock
            .Setup(db => db.GetMultipleItems(
                "permissions",
                It.Is<Dictionary<string, string>>(d => (string)d["value"] == "perm1"),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<int>(), It.IsAny<string?>(),
                CancellationToken.None))
            .ReturnsAsync(Result.Success(new List<Dictionary<string, string>>
            {
                    new Dictionary<string, string> { { "value", "perm1" } }
            }));
        // Second permission missing (empty list)
        _dynamoDbMock
            .Setup(db => db.GetMultipleItems(
                "permissions",
                It.Is<Dictionary<string, string>>(d => d["value"] == "perm2"),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<int>(), It.IsAny<string?>(),
                CancellationToken.None))
            .ReturnsAsync(Result.Success(new List<Dictionary<string, string>>()));

        var result = await _service.ExistsPermissions(TenantId, permissions, CancellationToken.None);

        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeFalse();
    }

    [Test]
    public async Task ExistsPermissions_DynamoDbFailure_ReturnsFailureWithError()
    {
        var permissions = new List<string> { "perm1", "perm2" };
        var expectedError = new Error("ERR", "failure");
        // First call fails
        _dynamoDbMock
            .Setup(db => db.GetMultipleItems(
                "permissions",
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<int>(), It.IsAny<string?>(),
                CancellationToken.None))
            .ReturnsAsync(Result.Failure<List<Dictionary<string, string>>>(expectedError));

        var result = await _service.ExistsPermissions(TenantId, permissions, CancellationToken.None);

        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(expectedError);
    }

    [Test]
    public async Task GetAllPermissions_Success_ReturnsMappedPermissions()
    {
        // Prepare mock data
        var dbItems = new List<Dictionary<string, string>>
            {
                new Dictionary<string, string>
                {
                    { "value", "perm1" },
                    { "color", "red" },
                    { "title_en", "Title1EN" },
                    { "title_es", "Title1ES" },
                    { "title_pt", "Title1PT" },
                    { "users_count", "5" }
                }
            };
        _dynamoDbMock
            .Setup(db => db.GetMultipleItems(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(), It.IsAny<string?>(),
                CancellationToken.None))
            .ReturnsAsync(Result.Success(dbItems));

        var result = await _service.GetAllPermissions(TenantId, CancellationToken.None);

        result.IsSuccess.Should().BeTrue();
        var permissions = result.Value;
        permissions.Should().HaveCount(1);
        var p = permissions[0];
        p.TenantId.Should().Be(TenantId);
        p.Value.Should().Be("perm1");
        p.Color.Should().Be("red");
        p.Title_En.Should().Be("Title1EN");
        p.Title_Es.Should().Be("Title1ES");
        p.Title_Pt.Should().Be("Title1PT");
        p.UsersCount.Should().Be(5);
    }

    [Test]
    public async Task GetAllPermissions_DynamoDbFailure_ReturnsFailure()
    {
        var expectedError = new Error("FAIL", "db error");
        _dynamoDbMock
            .Setup(db => db.GetMultipleItems(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(), It.IsAny<string?>(),
                CancellationToken.None))
            .ReturnsAsync(Result.Failure<List<Dictionary<string, string>>>(expectedError));

        var result = await _service.GetAllPermissions(TenantId, CancellationToken.None);

        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(expectedError);
    }


    [Test]
    public async Task AddPermissionUserCount_AllTasksSucceed_ReturnsSuccess()
    {
        var permissions = new List<string> { "perm1", "perm2" };
        int userCount = 3;

        foreach (var perm in permissions)
        {
            _dynamoDbMock
                .Setup(db => db.AddBulkAsync(
                    It.IsAny<string>(),
                    It.Is<Dictionary<string, string>>(d =>
                        d.ContainsKey("tenant_id") && d["tenant_id"] == TenantId &&
                        d.ContainsKey("value") && d["value"] == perm),
                    It.IsAny<Dictionary<string, object>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    CancellationToken.None))
                .ReturnsAsync(Result.Success());
        }

        var result = await _service.AddPermissionUserCount(TenantId, permissions, userCount, CancellationToken.None);

        result.IsSuccess.Should().BeTrue();
    }

    [Test]
    public async Task AddPermissionUserCount_Failure_ReturnsFailureWithError()
    {
        var permissions = new List<string> { "perm1", "perm2" };
        int userCount = 2;
        var expectedError = new Error("ERR", "bulk failure");

        // First succeeds
        _dynamoDbMock
            .Setup(db => db.AddBulkAsync(
                It.IsAny<string>(),
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                CancellationToken.None))
            .ReturnsAsync(Result.Success());
        // Second fails
        _dynamoDbMock
            .SetupSequence(db => db.AddBulkAsync(
                It.IsAny<string>(),
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                CancellationToken.None))
            .ReturnsAsync(Result.Success())
            .ReturnsAsync(Result.Failure(expectedError));

        var result = await _service.AddPermissionUserCount(TenantId, permissions, userCount, CancellationToken.None);

        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(expectedError);
    }
}
