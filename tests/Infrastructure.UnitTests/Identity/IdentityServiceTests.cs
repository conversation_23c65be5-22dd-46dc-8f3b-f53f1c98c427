﻿using System.Diagnostics;
using System.Threading;
using Amazon.CognitoIdentityProvider;
using Amazon.CognitoIdentityProvider.Model;
using FluentAssertions.Common;
using FluentAssertions.Equivalency;
using Microsoft.Extensions.Logging;
using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Domain.DTO;
using UsersManagement.Domain.Entities;
using UsersManagement.Domain.Enums;
using UsersManagement.Domain.Errors;
using UsersManagement.Infrastructure.Identity;
using LogLevel = Microsoft.Extensions.Logging.LogLevel;

namespace UsersManagement.Infrastructure.UnitTests.Identity;
public class IdentityServiceTests
{
    private Mock<IAmazonCognitoIdentityProvider> _cognitoMock;
    private Mock<ILicenseService> _licenseServiceMock;
    private Mock<ISettingsService> _settingsServiceMock;
    private Mock<ITenantProvider> _tenantProviderMock;
    private Mock<ILogger<IdentityService>> _identityServiceLoggerMock;

    private static readonly AdminInitiateAuthResponse AuthenticatedResponse = new()
    {
        AuthenticationResult = new()
        {
            AccessToken = "abc",
            ExpiresIn = 1,
            IdToken = "abc",
            RefreshToken = "abc",
            TokenType = "Bearer"
        },
        ContentLength = 1,
        HttpStatusCode = System.Net.HttpStatusCode.OK,
    };

    private static readonly AdminInitiateAuthResponse ChangePasswordResponse = new()
    {
        ChallengeName = ChallengeNameType.NEW_PASSWORD_REQUIRED,
        ContentLength = 1,
        HttpStatusCode = System.Net.HttpStatusCode.OK,
        Session = "12345678912345678912"
    };

    private const string USER_POOL_ID = "test_arg";

    [SetUp]
    public void Setup()
    {
        _identityServiceLoggerMock = new Mock<ILogger<IdentityService>>();

        _tenantProviderMock = new Mock<ITenantProvider>();

        _tenantProviderMock
            .Setup(x => x.GetTenantId())
            .Returns(USER_POOL_ID);
        _tenantProviderMock
            .Setup(x => x.GetTenantName())
            .Returns("TenantName");
        _tenantProviderMock
            .Setup(x => x.GetTenantCountry())
            .Returns("TenantCountry");

        _settingsServiceMock = new Mock<ISettingsService>();

        _settingsServiceMock
            .Setup(x => x.GetConfiguration(It.IsAny<Application.Common.Models.Settings>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("key"));

        _licenseServiceMock = new Mock<ILicenseService>();
        _licenseServiceMock
            .Setup(x => x.GetLicenseAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success(new License(USER_POOL_ID, 10, 1, DateTime.Now.AddDays(10))));

        _cognitoMock = new Mock<IAmazonCognitoIdentityProvider>();
        _cognitoMock
            .Setup(x => x.AdminInitiateAuthAsync(It.IsAny<AdminInitiateAuthRequest>(), It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(AuthenticatedResponse));

        _cognitoMock
            .Setup(x => x.AdminInitiateAuthAsync(It.Is<AdminInitiateAuthRequest>(x => x.AuthParameters["USERNAME"] == "<EMAIL>"), It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(ChangePasswordResponse));

        _cognitoMock
            .Setup(x => x.ListUserPoolsAsync(It.IsAny<ListUserPoolsRequest>(), It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(new ListUserPoolsResponse
            {
                ContentLength = 1,
                HttpStatusCode = System.Net.HttpStatusCode.OK,
                UserPools = new()
                {
                    new()
                    {
                        Id = USER_POOL_ID,
                        CreationDate = DateTime.Now,
                        Name = "TenantName-TenantCountry"
                    }
                }
            }));

        _cognitoMock
            .Setup(x => x.AdminRespondToAuthChallengeAsync(It.IsAny<AdminRespondToAuthChallengeRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new AdminRespondToAuthChallengeResponse()
            {
                AuthenticationResult = AuthenticatedResponse.AuthenticationResult,
                ChallengeName = AuthenticatedResponse.ChallengeName,
                ChallengeParameters = AuthenticatedResponse.ChallengeParameters,
                ContentLength = AuthenticatedResponse.ContentLength,
                HttpStatusCode = AuthenticatedResponse.HttpStatusCode,
                ResponseMetadata = AuthenticatedResponse.ResponseMetadata,
                Session = AuthenticatedResponse.Session
            });

        _cognitoMock
            .Setup(x => x.AdminRespondToAuthChallengeAsync(It.Is<AdminRespondToAuthChallengeRequest>(x => x.ChallengeName == ChallengeNameType.SELECT_MFA_TYPE), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new AdminRespondToAuthChallengeResponse()
            {
                ChallengeName = ChallengeNameType.SOFTWARE_TOKEN_MFA,
                ContentLength = 1,
                HttpStatusCode = System.Net.HttpStatusCode.OK,
                Session = ChangePasswordResponse.Session
            });

        _cognitoMock
            .Setup(x => x.ListUsersAsync(It.IsAny<ListUsersRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ListUsersResponse()
            {
                ContentLength = 1,
                HttpStatusCode = System.Net.HttpStatusCode.OK,
                Users = new()
                {
                    new()
                    {
                        Enabled = true,
                        Username = "<EMAIL>",
                        UserStatus = UserStatusType.CONFIRMED,
                        UserLastModifiedDate = DateTime.UtcNow,
                        UserCreateDate = DateTime.UtcNow,
                        Attributes = new()
                        {
                            new()
                            {
                                Name = "name",
                                Value = "Nombre"
                            },
                            new()
                            {
                                Name = "family_name",
                                Value = "Apellido"
                            },
                            new()
                            {
                                Name = "email",
                                Value = "<EMAIL>"
                            },
                            new()
                            {
                                Name = CognitoConstants.Attributes.ROLE,
                                Value = UserRole.Standard.ToString()
                            }
                        }
                    }
                }
            });

        _cognitoMock
            .Setup(x => x.AdminCreateUserAsync(It.IsAny<AdminCreateUserRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((AdminCreateUserRequest request, CancellationToken cancellationToken) => new AdminCreateUserResponse()
            {
                ContentLength = 1,
                HttpStatusCode = System.Net.HttpStatusCode.OK,
                User = new()
                {
                    Username = request.Username,
                    Attributes = request.UserAttributes,
                    Enabled = true,
                    UserCreateDate = DateTime.Now,
                    UserLastModifiedDate = DateTime.Now,
                    UserStatus = UserStatusType.FORCE_CHANGE_PASSWORD
                }
            });

        _cognitoMock
            .Setup(x => x.AdminCreateUserAsync(It.Is<AdminCreateUserRequest>(x => x.Username == "<EMAIL>"), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception());

        _cognitoMock
            .Setup(x => x.AdminDeleteUserAsync(It.IsAny<AdminDeleteUserRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new AdminDeleteUserResponse()
            {
                ContentLength = 1,
                HttpStatusCode = System.Net.HttpStatusCode.OK
            });

        _cognitoMock
            .Setup(x => x.RevokeTokenAsync(It.IsAny<RevokeTokenRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new RevokeTokenResponse()
            {
                ContentLength = 1,
                HttpStatusCode = System.Net.HttpStatusCode.OK
            });
    }

    [Test]
    public async Task LoginShouldLoginOk()
    {
        using var activity = new Activity("TestActivity");
        Activity.Current = activity;
        activity.Start();

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.LoginAsync(_tenantProviderMock.Object.GetTenantId(), "<EMAIL>", "ABC1234", new CancellationToken());

        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();

        result.Value.Should().NotBeNull();
        result.Value.ChallengeName.Should().BeNull();
        result.Value.AccessToken.Should().Be(AuthenticatedResponse.AuthenticationResult.AccessToken);
        result.Value.Session.Should().Be(AuthenticatedResponse.Session);
        result.Value.TenantId.Should().Be(USER_POOL_ID);

        Activity.Current.Tags.FirstOrDefault(tag => tag.Key == "platform.auth.user").Value.Should().Be("<EMAIL>");
    }

    [Test]
    public async Task LoginShouldRequirePasswordChange()
    {
        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.LoginAsync(_tenantProviderMock.Object.GetTenantId(), "<EMAIL>", "ABC1234", new CancellationToken());

        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();

        result.Value.Should().NotBeNull();
        result.Value.ChallengeName.Should().Be(ChangePasswordResponse.ChallengeName);
        result.Value.AccessToken.Should().BeNull();
        result.Value.Session.Should().Be(ChangePasswordResponse.Session);
        result.Value.TenantId.Should().Be(USER_POOL_ID);
    }

    [Test]
    public async Task LoginShouldReturnErrorIfSettingsThrow()
    {
        _settingsServiceMock
            .Setup(x => x.GetConfiguration(It.IsAny<Application.Common.Models.Settings>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception());

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.LoginAsync(_tenantProviderMock.Object.GetTenantId(), "<EMAIL>", "ABC1234", new CancellationToken());

        result.Should().NotBeNull();
        result.IsFailure.Should().BeTrue();

        result.Error.Should().Be(IdentityErrors.Cognito.LOGIN_GENERIC);

        _identityServiceLoggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Test]
    public async Task LoginShouldReturnErrorClientIdNotFound()
    {
        var settingsServiceMock = new Mock<ISettingsService>();
        settingsServiceMock
            .Setup(x => x.GetConfiguration(It.IsAny<Application.Common.Models.Settings>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("key"));
        settingsServiceMock
            .Setup(x => x.GetConfiguration(It.Is<Application.Common.Models.Settings>(x => x == Application.Common.Models.Settings.CognitoClientId), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure<string>(new Error("Test", "Test")));

        var identityService = new IdentityService(_cognitoMock.Object, settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.LoginAsync(_tenantProviderMock.Object.GetTenantId(), "<EMAIL>", "ABC1234", new CancellationToken());

        result.Should().NotBeNull();
        result.IsFailure.Should().BeTrue();

        result.Error.Should().Be(IdentityErrors.Cognito.LOGIN_GENERIC);

        _identityServiceLoggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Test]
    public async Task LoginShouldReturnErrorClientSecretNotFound()
    {
        var settingsServiceMock = new Mock<ISettingsService>();
        settingsServiceMock
            .Setup(x => x.GetConfiguration(It.IsAny<Application.Common.Models.Settings>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("key"));
        settingsServiceMock
            .Setup(x => x.GetConfiguration(It.Is<Application.Common.Models.Settings>(x => x == Application.Common.Models.Settings.CognitoSecretHash), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure<string>(new Error("Test", "Test")));

        var identityService = new IdentityService(_cognitoMock.Object, settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.LoginAsync(_tenantProviderMock.Object.GetTenantId(), "<EMAIL>", "ABC1234", new CancellationToken());

        result.Should().NotBeNull();
        result.IsFailure.Should().BeTrue();

        result.Error.Should().Be(IdentityErrors.Cognito.LOGIN_GENERIC);

        _identityServiceLoggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Test]
    public async Task LoginShouldReturnErrorUserPoolNotFound()
    {
        var cognitoMock = new Mock<IAmazonCognitoIdentityProvider>();

        cognitoMock
            .Setup(x => x.ListUserPoolsAsync(It.IsAny<ListUserPoolsRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ListUserPoolsResponse()
            {
                HttpStatusCode = System.Net.HttpStatusCode.OK,
                UserPools = new()
                {
                    new()
                    {
                        Id = "test_test",
                        CreationDate = DateTime.Now,
                        Name = "tenant-not-found"
                    }
                }
            });
        var loggerMock = _identityServiceLoggerMock;

        var identityService = new IdentityService(cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, loggerMock.Object);

        var result = await identityService.LoginAsync(_tenantProviderMock.Object.GetTenantId(), "<EMAIL>", "ABC1234", new CancellationToken());

        result.Should().NotBeNull();
        result.IsFailure.Should().BeTrue();

        result.Error.Should().Be(IdentityErrors.Cognito.LOGIN_GENERIC);
    }

    [Test]
    public async Task LoginShouldReturnErrorUserPoolRequestFailed()
    {
        var cognitoMock = new Mock<IAmazonCognitoIdentityProvider>();

        cognitoMock
            .Setup(x => x.ListUserPoolsAsync(It.IsAny<ListUserPoolsRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception());

        var identityService = new IdentityService(cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.LoginAsync(_tenantProviderMock.Object.GetTenantId(), "<EMAIL>", "ABC1234", new CancellationToken());

        result.Should().NotBeNull();
        result.IsFailure.Should().BeTrue();

        result.Error.Code.Should().NotBeNullOrWhiteSpace();
        result.Error.Message.Should().NotBeNullOrWhiteSpace();

        _identityServiceLoggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Test]
    public async Task LoginShouldReturnTypedErrorUserDisabled()
    {
        var cognitoMock = new Mock<IAmazonCognitoIdentityProvider>();

        cognitoMock
            .Setup(x => x.ListUserPoolsAsync(It.IsAny<ListUserPoolsRequest>(), It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(new ListUserPoolsResponse
            {
                ContentLength = 1,
                HttpStatusCode = System.Net.HttpStatusCode.OK,
                UserPools = new()
                {
                    new()
                    {
                        Id = USER_POOL_ID,
                        CreationDate = DateTime.Now,
                        Name = "TenantName-TenantCountry"
                    }
                }
            }));

        cognitoMock
            .Setup(x => x.AdminInitiateAuthAsync(It.IsAny<AdminInitiateAuthRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new NotAuthorizedException("User is disabled."));

        var identityService = new IdentityService(cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.LoginAsync(_tenantProviderMock.Object.GetTenantId(), "<EMAIL>", "ABC1234", new CancellationToken());

        result.Should().NotBeNull();
        result.IsFailure.Should().BeTrue();

        result.Error.Should().Be(IdentityErrors.Cognito.DISABLED_USER);

        _identityServiceLoggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Never);
    }

    [Test]
    public async Task LoginShouldReturnTypedErrorIncorrectPassword()
    {
        var cognitoMock = new Mock<IAmazonCognitoIdentityProvider>();

        cognitoMock
            .Setup(x => x.ListUserPoolsAsync(It.IsAny<ListUserPoolsRequest>(), It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(new ListUserPoolsResponse
            {
                ContentLength = 1,
                HttpStatusCode = System.Net.HttpStatusCode.OK,
                UserPools = new()
                {
                    new()
                    {
                        Id = USER_POOL_ID,
                        CreationDate = DateTime.Now,
                        Name = "TenantName-TenantCountry"
                    }
                }
            }));

        cognitoMock
            .Setup(x => x.AdminInitiateAuthAsync(It.IsAny<AdminInitiateAuthRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new NotAuthorizedException("Incorrect username or password."));

        var loggerMock = _identityServiceLoggerMock;
        var identityService = new IdentityService(cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, loggerMock.Object);

        var result = await identityService.LoginAsync(_tenantProviderMock.Object.GetTenantId(), "<EMAIL>", "ABC1234", new CancellationToken());

        result.Should().NotBeNull();
        result.IsFailure.Should().BeTrue();

        result.Error.Should().Be(IdentityErrors.Cognito.INCORRECT_USER_PASS);

        loggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Never);
    }

    [Test]
    public async Task LoginThrowsNotAuthorizedNonHandledShouldReturnTypedErrorGeneric()
    {
        var cognitoMock = new Mock<IAmazonCognitoIdentityProvider>();

        cognitoMock
            .Setup(x => x.ListUserPoolsAsync(It.IsAny<ListUserPoolsRequest>(), It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(new ListUserPoolsResponse
            {
                ContentLength = 1,
                HttpStatusCode = System.Net.HttpStatusCode.OK,
                UserPools = new()
                {
                    new()
                    {
                        Id = USER_POOL_ID,
                        CreationDate = DateTime.Now,
                        Name = "TenantName-TenantCountry"
                    }
                }
            }));

        cognitoMock
            .Setup(x => x.AdminInitiateAuthAsync(It.IsAny<AdminInitiateAuthRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new NotAuthorizedException("Non handled exception."));

        var identityService = new IdentityService(cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.LoginAsync(_tenantProviderMock.Object.GetTenantId(), "<EMAIL>", "ABC1234", new CancellationToken());

        result.Should().NotBeNull();
        result.IsFailure.Should().BeTrue();

        result.Error.Should().Be(IdentityErrors.Cognito.LOGIN_GENERIC);

        _identityServiceLoggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Test]
    public async Task LoginShouldLoginOkUserPoolIteration()
    {
        var cognitoMock = new Mock<IAmazonCognitoIdentityProvider>();
        cognitoMock
            .Setup(x => x.AdminInitiateAuthAsync(It.IsAny<AdminInitiateAuthRequest>(), It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(AuthenticatedResponse));

        var userPools = Enumerable.Range(0, 30)
            .Select(x => new UserPoolDescriptionType()
            {
                Id = $"{USER_POOL_ID}_{x}",
                CreationDate = DateTime.Now,
                LastModifiedDate = DateTime.Now,
                Name = $"{x}"
            }).ToList();

        UserPoolDescriptionType userPoolToMatch = new()
        {
            Id = USER_POOL_ID,
            CreationDate = DateTime.Now,
            Name = "TenantName-TenantCountry"
        };
        userPools.Add(userPoolToMatch);

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.LoginAsync(_tenantProviderMock.Object.GetTenantId(), "<EMAIL>", "ABC1234", new CancellationToken());

        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();

        result.Value.Should().NotBeNull();
        result.Value.ChallengeName.Should().BeNull();
        result.Value.AccessToken.Should().Be(AuthenticatedResponse.AuthenticationResult.AccessToken);
        result.Value.Session.Should().Be(AuthenticatedResponse.Session);
        result.Value.TenantId.Should().Be(USER_POOL_ID);
    }

    [Test]
    public async Task AuthChallengeShouldLoginOk()
    {
        using var activity = new Activity("TestActivity");
        Activity.Current = activity;
        activity.Start();

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.RespondToAuthChallengeAsync(USER_POOL_ID, "asd", "<EMAIL>", ChallengeNameType.NEW_PASSWORD_REQUIRED, new Dictionary<string, string>
        {
            {CognitoChallengeValidator.NEW_PASSWORD, "PASSWORD" }
        }, new CancellationToken());

        result.IsSuccess.Should().BeTrue();
        result.Value.Should().NotBeNull();


        result.Value.Should().NotBeNull();
        result.Value.ChallengeName.Should().BeNull();
        result.Value.AccessToken.Should().Be(AuthenticatedResponse.AuthenticationResult.AccessToken);
        result.Value.Session.Should().Be(AuthenticatedResponse.Session);
        result.Value.TenantId.Should().Be(USER_POOL_ID);

        Activity.Current.Tags.FirstOrDefault(tag => tag.Key == "platform.auth.user").Value.Should().Be("<EMAIL>");
        Activity.Current.Tags.FirstOrDefault(tag => tag.Key == "platform.auth.challenge_name").Value.Should().Be(ChallengeNameType.NEW_PASSWORD_REQUIRED);
    }

    [Test]
    public async Task AuthChallengeShouldReturnErrorRespondAuthRequestFailed()
    {
        var cognitoMock = new Mock<IAmazonCognitoIdentityProvider>();
        cognitoMock
            .Setup(x => x.AdminRespondToAuthChallengeAsync(It.IsAny<AdminRespondToAuthChallengeRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception());

        var identityService = new IdentityService(cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.RespondToAuthChallengeAsync("test-tenant", "asd", "<EMAIL>", ChallengeNameType.NEW_PASSWORD_REQUIRED, new Dictionary<string, string>
        {
            {CognitoChallengeValidator.NEW_PASSWORD, "PASSWORD" }
        }, new CancellationToken());

        result.Should().NotBeNull();
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().NotBeNullOrWhiteSpace();
        result.Error.Message.Should().NotBeNullOrWhiteSpace();
    }

    [Test]
    public async Task AuthChallengeShouldReturnErrorClientIdNotFound()
    {
        var settingsServiceMock = new Mock<ISettingsService>();
        settingsServiceMock
            .Setup(x => x.GetConfiguration(It.IsAny<Application.Common.Models.Settings>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success("key"));
        settingsServiceMock
            .Setup(x => x.GetConfiguration(It.Is<Application.Common.Models.Settings>(x => x == Application.Common.Models.Settings.CognitoClientId), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure<string>(new Error("Test", "Test")));
        var loggerMock = _identityServiceLoggerMock;
        var identityService = new IdentityService(_cognitoMock.Object, settingsServiceMock.Object, _tenantProviderMock.Object, loggerMock.Object);

        var result = await identityService.RespondToAuthChallengeAsync("test-tenant", "asd", "<EMAIL>", ChallengeNameType.NEW_PASSWORD_REQUIRED, new Dictionary<string, string>
        {
            {CognitoChallengeValidator.NEW_PASSWORD, "PASSWORD" }
        }, new CancellationToken());


        result.Should().NotBeNull();
        result.IsFailure.Should().BeTrue();

        result.Error.Code.Should().NotBeNullOrWhiteSpace();
        result.Error.Message.Should().NotBeNullOrWhiteSpace();

        loggerMock.Verify(x => x.Log(
            It.Is<LogLevel>(x => x == LogLevel.Error),
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<object, Exception?, string>>()), Times.Never);
    }

    [Test]
    public async Task AuthChallengeShouldReturnErrorInvalidPasswordError()
    {
        var cognitoMock = new Mock<IAmazonCognitoIdentityProvider>();
        cognitoMock
            .Setup(x => x.AdminRespondToAuthChallengeAsync(It.IsAny<AdminRespondToAuthChallengeRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidPasswordException("Invalid password test"));

        var identityService = new IdentityService(cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.RespondToAuthChallengeAsync("test-tenant", "asd", "<EMAIL>", ChallengeNameType.NEW_PASSWORD_REQUIRED, new Dictionary<string, string>
        {
            {CognitoChallengeValidator.NEW_PASSWORD, "PASSWORD" }
        }, new CancellationToken());

        result.Should().NotBeNull();
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().NotBeNullOrWhiteSpace();
        result.Error.Should().Be(IdentityErrors.Cognito.INVALID_PASS_FORMAT);
    }

    [Test]
    public async Task AuthChallengeShouldRequireAnotherChallenge()
    {
        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.RespondToAuthChallengeAsync(USER_POOL_ID, "asd", "<EMAIL>", ChallengeNameType.SELECT_MFA_TYPE, new Dictionary<string, string>
        {
            {CognitoChallengeValidator.ANSWER, "SOFTWARE" }
        }, new CancellationToken());

        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();

        result.Value.Should().NotBeNull();
        result.Value.ChallengeName.Should().Be(ChallengeNameType.SOFTWARE_TOKEN_MFA);
        result.Value.AccessToken.Should().BeNull();
        result.Value.Session.Should().Be(ChangePasswordResponse.Session);
        result.Value.TenantId.Should().Be(USER_POOL_ID);
    }

    [Test]
    public async Task GetUsersShouldReturnUsers()
    {
        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.GetUsersAsync(null, 10, new CancellationToken());

        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();

        result.Value.Should().NotBeNull();
        result.Value.users.Count.Should().Be(1);
        result.Value.users.First().Email.Should().NotBeNullOrWhiteSpace();
        result.Value.users.First().Confirmed.Should().BeTrue();
        result.Value.users.First().Name.Should().NotBeNullOrWhiteSpace();
        result.Value.users.First().Surname.Should().NotBeNullOrWhiteSpace();
    }

    [Test]
    public async Task GetUsersEmptyNameAndSurnameShouldReturnUsers()
    {
        var cognitoMock = new Mock<IAmazonCognitoIdentityProvider>();

        cognitoMock
            .Setup(x => x.ListUsersAsync(It.IsAny<ListUsersRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ListUsersResponse()
            {
                ContentLength = 1,
                HttpStatusCode = System.Net.HttpStatusCode.OK,
                Users = new()
                {
                    new()
                    {
                        Enabled = true,
                        Username = "<EMAIL>",
                        UserStatus = UserStatusType.CONFIRMED,
                        UserLastModifiedDate = DateTime.UtcNow,
                        UserCreateDate = DateTime.UtcNow,
                        Attributes = new()
                        {
                            new()
                            {
                                Name = "email",
                                Value = "<EMAIL>"
                            },
                            new()
                            {
                                Name = "custom:role",
                                Value = UserRole.Standard.ToString()
                            },
                            new()
                            {
                                Name = "custom:origin",
                                Value = UserOrigin.Federated.ToString()
                            }
                        }
                    }
                }
            });

        var identityService = new IdentityService(cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.GetUsersAsync(null, 10, new CancellationToken());

        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();

        result.Value.Should().NotBeNull();
        result.Value.users.Count.Should().Be(1);
        result.Value.users.First().Email.Should().NotBeNullOrWhiteSpace();
        result.Value.users.First().Confirmed.Should().BeTrue();
        result.Value.users.First().Name.Should().BeNullOrWhiteSpace();
        result.Value.users.First().Surname.Should().BeNullOrWhiteSpace();
    }

    [Test]
    public async Task GetUsersCognitoThrowShouldReturnError()
    {
        var cognitoMock = new Mock<IAmazonCognitoIdentityProvider>();

        cognitoMock
            .Setup(x => x.ListUsersAsync(It.IsAny<ListUsersRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception());

        var identityService = new IdentityService(cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.GetUsersAsync(null, 10, new CancellationToken());

        result.Should().NotBeNull();
        result.IsFailure.Should().BeTrue();

        result.Error.Should().NotBeNull();
        result.Error.Code.Should().NotBeNullOrWhiteSpace();
        result.Error.Message.Should().NotBeNullOrWhiteSpace();
    }

    [Test]
    public async Task CreateUsersShouldReturnSuccess()
    {
        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.CreateUsersAsync(_tenantProviderMock.Object.GetTenantId(), new List<User>()
        {
            User.Create(null, null, "<EMAIL>", UserRole.Standard, UserOrigin.Standalone, User.DefaultPermissions),
            User.Create(null, null, "<EMAIL>", UserRole.Standard, UserOrigin.Standalone, User.DefaultPermissions),
            User.Create(null, null, "<EMAIL>", UserRole.Standard, UserOrigin.Standalone, User.DefaultPermissions),

        }, cancellationToken: new CancellationToken());

        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
    }

    [Test]
    public async Task CreateUsersShouldReturnError()
    {
        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.CreateUsersAsync(_tenantProviderMock.Object.GetTenantId(), new List<User>()
        {
            User.Create(null, null, "<EMAIL>", UserRole.Standard, UserOrigin.Standalone, User.DefaultPermissions),
            User.Create(null, null, "<EMAIL>", UserRole.Standard, UserOrigin.Standalone, User.DefaultPermissions),
            User.Create(null, null, "<EMAIL>", UserRole.Standard, UserOrigin.Standalone, User.DefaultPermissions),

        }, cancellationToken: new CancellationToken());

        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();

        result.Value.createdQty.Should().Be(2);
        result.Value.errors.Should().Contain("<EMAIL>", "This email is supposed to fail when created");
    }

    [Test]
    public async Task CreateUserWithTemporaryPasswordReturnsSuccess()
    {
        _cognitoMock
            .Setup(x => x.AdminCreateUserAsync(It.Is<AdminCreateUserRequest>(x => x.TemporaryPassword == "generatedPassword"), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new AdminCreateUserResponse()
            {
                HttpStatusCode = System.Net.HttpStatusCode.OK,
                User = new UserType()
                {

                }
            });

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var result = await identityService.CreateUsersAsync(_tenantProviderMock.Object.GetTenantId(), new List<User>()
        {
            User.Create(null, null, "<EMAIL>", UserRole.Standard, UserOrigin.Standalone, User.DefaultPermissions),
            User.Create(null, null, "<EMAIL>", UserRole.Standard, UserOrigin.Standalone, User.DefaultPermissions),
            User.Create(null, null, "<EMAIL>", UserRole.Standard, UserOrigin.Standalone, User.DefaultPermissions),

        }, "generatedPassword", new CancellationToken());

        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
    }

    [Test]
    public async Task GetTenantIdByNameAndCountryExceptionShouldReturnError()
    {
        _cognitoMock.Setup(x => x.ListUserPoolsAsync(It.IsAny<ListUserPoolsRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test error"));

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);
        var result = await identityService.GetTenantIdByNameAndCountry("test", "fail", CancellationToken.None);

        result.IsFailure.Should().BeTrue();

        result.Error.Should().BeEquivalentTo(IdentityErrors.Cognito.USER_POOL_NOT_FOUND);

        _identityServiceLoggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Test]
    public async Task GetTenantIdByNameAndCountryNotFoundShouldReturnError()
    {
        _cognitoMock.Setup(x => x.ListUserPoolsAsync(It.IsAny<ListUserPoolsRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ListUserPoolsResponse
            {
                ContentLength = 1,
                HttpStatusCode = System.Net.HttpStatusCode.OK,
                NextToken = null,
                UserPools = new()
                {
                    {
                        new()
                        {
                            Id = "1_a",
                            CreationDate = DateTime.Now,
                            LambdaConfig = null,
                            LastModifiedDate = DateTime.Now,
                            Name = "test"
                        }
                    }
                }
            });

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);
        var result = await identityService.GetTenantIdByNameAndCountry("test", "fail", CancellationToken.None);

        result.IsFailure.Should().BeTrue();

        result.Error.Should().BeEquivalentTo(IdentityErrors.Cognito.USER_POOL_NOT_FOUND);

        _identityServiceLoggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Never);
    }

    [Test]
    public async Task DeleteUserAsyncShouldReturnSuccess()
    {
        _licenseServiceMock
            .Setup(x => x.Refund(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var response = await identityService.DeleteUserAsync("<EMAIL>", CancellationToken.None);

        response.IsSuccess.Should().BeTrue();
    }

    [Test]
    public async Task DeleteUserAsyncCognitoDeleteFailShouldReturnError()
    {
        _cognitoMock
            .Setup(x => x.AdminDeleteUserAsync(It.IsAny<AdminDeleteUserRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("test"));

        _licenseServiceMock
            .Setup(x => x.Refund(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var response = await identityService.DeleteUserAsync("<EMAIL>", CancellationToken.None);

        response.IsFailure.Should().BeTrue();
        response.Error.Should().NotBeNull();
        response.Error.Should().Be(IdentityErrors.Cognito.DELETE_FAIL);
    }

    [Test]
    public async Task RevokeTokenAsyncSouldReturnSuccess()
    {
        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var response = await identityService.RevokeTokenAsync(_tenantProviderMock.Object.GetTenantId(), "abcde", CancellationToken.None);

        response.IsSuccess.Should().BeTrue();
    }

    [Test]
    public async Task RevokeTokenAsyncSettingsErrorSouldReturnFailure()
    {
        _settingsServiceMock
            .Setup(x => x.GetConfiguration(It.IsAny<Application.Common.Models.Settings>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure<string>(new Error("Test error", "Test error")));

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var response = await identityService.RevokeTokenAsync(_tenantProviderMock.Object.GetTenantId(), "abcde", CancellationToken.None);

        response.IsFailure.Should().BeTrue();
        response.Error.Should().Be(IdentityErrors.Cognito.REVOKE_FAIL);
        _identityServiceLoggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Test]
    public async Task RevokeTokenAsyncCognitoErrorSouldReturnFailure()
    {
        _cognitoMock
            .Setup(x => x.RevokeTokenAsync(It.IsAny<RevokeTokenRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception());

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        var response = await identityService.RevokeTokenAsync(_tenantProviderMock.Object.GetTenantId(), "abcde", CancellationToken.None);

        response.IsFailure.Should().BeTrue();
        response.Error.Should().Be(IdentityErrors.Cognito.REVOKE_FAIL);

        _identityServiceLoggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Test]
    public async Task ForceChangePassword_Success_ReturnsSuccessResult()
    {
        // Arrange
        var tenantId = "tenant-id";
        var email = "<EMAIL>";
        var password = Guid.NewGuid().ToString();
        _cognitoMock
            .Setup(x => x.AdminSetUserPasswordAsync(It.IsAny<AdminSetUserPasswordRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new AdminSetUserPasswordResponse());

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.ForceChangePassword(tenantId, email, password, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Test]
    public async Task ForceChangePassword_WhenExceptionThrown_ReturnsFailureResult_AndLogsError()
    {
        // Arrange
        var tenantId = "tenant-id";
        var email = "<EMAIL>";
        var password = Guid.NewGuid().ToString();
        var exception = new Exception("Test exception");
        var logger = _identityServiceLoggerMock;

        _cognitoMock
            .Setup(x => x.AdminSetUserPasswordAsync(It.IsAny<AdminSetUserPasswordRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);
        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, logger.Object);


        // Act
        var result = await identityService.ForceChangePassword(tenantId, email, password, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(IdentityErrors.Cognito.FORCE_PASSWORD_CHANGE_FAIL);

        logger.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Test]
    public async Task UserExists_WhenUserFound_ReturnsTrue()
    {
        // Arrange
        string tenantId = "tenant-id";
        string email = "<EMAIL>";
        _cognitoMock
            .Setup(x => x.AdminGetUserAsync(It.Is<AdminGetUserRequest>(req => req.Username == email && req.UserPoolId == tenantId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new AdminGetUserResponse());

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.UserExists(tenantId, email, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeTrue();
    }

    [Test]
    public async Task UserExists_WhenUserNotFoundExceptionThrown_ReturnsFalse()
    {
        // Arrange
        string tenantId = "tenant-id";
        string email = "<EMAIL>";

        _cognitoMock
            .Setup(x => x.AdminGetUserAsync(It.IsAny<AdminGetUserRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new UserNotFoundException("User not found"));

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.UserExists(tenantId, email, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeFalse();
    }

    [Test]
    public async Task UserExists_WhenOtherExceptionThrown_ReturnsFailureAndLogsError()
    {
        // Arrange
        string tenantId = "tenant-id";
        string email = "<EMAIL>";
        var exception = new Exception("Unexpected error");
        _cognitoMock
            .Setup(x => x.AdminGetUserAsync(It.IsAny<AdminGetUserRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.UserExists(tenantId, email, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(IdentityErrors.Cognito.ERROR_OBTAINING_USER);

        _identityServiceLoggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Test]
    public async Task GetUserAsync_Success_ReturnsMappedUser()
    {
        // Arrange
        string email = "<EMAIL>";
        _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(USER_POOL_ID);
        var response = new AdminGetUserResponse
        {
            Username = email,
            UserStatus = UserStatusType.CONFIRMED,
            Enabled = true,
            UserAttributes = new List<AttributeType>
                {
                    new AttributeType { Name = "name", Value = "John" },
                    new AttributeType { Name = "family_name", Value = "Doe" },
                    new AttributeType { Name = "email", Value = email },
                    new AttributeType { Name = CognitoConstants.Attributes.ROLE, Value = UserRole.Administrator.ToString() },
                    // ORIGIN missing => default
                    // PERMISSIONS missing => default empty
                }
        };
        _cognitoMock
            .Setup(c => c.AdminGetUserAsync(
                It.Is<AdminGetUserRequest>(r => r.Username == email && r.UserPoolId == USER_POOL_ID),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.GetUserAsync(email, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        var user = result.Value;
        user.Email.Should().BeEquivalentTo(email);
        user.Name.Should().Be("John");
        user.Surname.Should().Be("Doe");
        user.Role.Should().Be(UserRole.Administrator);
        user.Confirmed.Should().BeTrue();
        user.Origin.Should().Be(UserOrigin.Standalone);
        user.Permissions.Should().BeEmpty();
    }

    [Test]
    public async Task GetUserAsync_WithOptionalAttributes_ReturnsMappedUser()
    {
        // Arrange
        string email = "<EMAIL>";
        _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(USER_POOL_ID);
        var response = new AdminGetUserResponse
        {
            Username = email,
            UserStatus = UserStatusType.UNCONFIRMED,
            Enabled = false,
            UserAttributes = new List<AttributeType>
                {
                    new AttributeType { Name = "name", Value = "Jane" },
                    new AttributeType { Name = "family_name", Value = "Smith" },
                    new AttributeType { Name = "email", Value = email },
                    new AttributeType { Name = CognitoConstants.Attributes.ROLE, Value = UserRole.Standard.ToString() },
                    new AttributeType { Name = CognitoConstants.Attributes.ORIGIN, Value = UserOrigin.Federated.ToString() },
                    new AttributeType { Name = CognitoConstants.Attributes.PERMISSIONS, Value = "p1,p2" }
                }
        };
        _cognitoMock
        .Setup(c => c.AdminGetUserAsync(
                It.IsAny<AdminGetUserRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.GetUserAsync(email, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        var user = result.Value;
        user.Name.Should().Be("Jane");
        user.Surname.Should().Be("Smith");
        user.Role.Should().Be(UserRole.Standard);
        user.Confirmed.Should().BeFalse();
        user.Origin.Should().Be(UserOrigin.Federated);
        user.Permissions.Should().BeEquivalentTo(["p1", "p2"]);
    }

    [Test]
    public async Task GetUserAsync_CognitoThrows_ReturnsFailure()
    {
        // Arrange
        string email = "<EMAIL>";
        _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(USER_POOL_ID);
        var exception = new Exception("cognito error");
        _cognitoMock
            .Setup(c => c.AdminGetUserAsync(It.IsAny<AdminGetUserRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.GetUserAsync(email, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("IdentityService.GetUserAsync");
    }

    [Test]
    public async Task EditUserAsync_NoChanges_ReturnsFailureNotingToUpdate()
    {
        // Arrange
        string email = "<EMAIL>";
        var request = new UpdateUserRequest();
        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.EditUserAsync(email, request, USER_POOL_ID, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(IdentityErrors.Cognito.UPDATE_USER_NOTHING_TO_UPDATE);
    }

    [Test]
    public async Task EditUserAsync_AllFields_Succeeds()
    {
        // Arrange
        string email = "<EMAIL>";
        var request = new UpdateUserRequest
        {
            Permissions = new List<string> { "p1", "p2" },
            NewEmail = "<EMAIL>",
            Role = UserRole.Administrator
        };
        _cognitoMock
            .Setup(c => c.AdminUpdateUserAttributesAsync(
                It.IsAny<AdminUpdateUserAttributesRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new AdminUpdateUserAttributesResponse()
            {
                ContentLength = 1,
                HttpStatusCode = System.Net.HttpStatusCode.OK,
                ResponseMetadata = new()
            });

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.EditUserAsync(email, request, USER_POOL_ID, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Test]
    public async Task EditUserAsync_CognitoThrows_ReturnsFailure()
    {
        // Arrange
        string email = "<EMAIL>";
        var request = new UpdateUserRequest
        {
            NewEmail = "<EMAIL>"
        };
        var exception = new Exception("update error");

        _cognitoMock
            .Setup(c => c.AdminUpdateUserAttributesAsync(It.IsAny<AdminUpdateUserAttributesRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.EditUserAsync(email, request, USER_POOL_ID, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(IdentityErrors.Cognito.UPDATE_USER_FAIL);
    }

    [Test]
    public async Task RefreshTokenAsync_ClientCredentialsFailure_ReturnsFailureAndLogsError()
    {
        // Arrange
        string tenantId = "tenantX";
        string email = "<EMAIL>";
        string refreshTokenValue = "asd";
        var credError = new Error("CRED_ERR", "no creds");
        _settingsServiceMock.Setup(s => s.GetConfiguration(It.IsAny<Application.Common.Models.Settings>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure<string>(credError));
        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);
        // Act
        var result = await identityService.RefreshTokenAsync(tenantId, email, refreshTokenValue, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(IdentityErrors.Cognito.REFRESH_GENERIC);
    }

    [Test]
    public async Task RefreshTokenAsync_Success_ReturnsLoginResponse()
    {
        // Arrange
        string tenantId = "tenantX";
        string email = "<EMAIL>";
        string refreshTokenValue = "asd";

        var authResult = new AuthenticationResultType
        {
            AccessToken = "accessToken",
            RefreshToken = "newRefreshToken"
        };
        var response = new AdminInitiateAuthResponse
        {
            ChallengeName = "challengeName",
            Session = "sessionValuesessionValue",
            AuthenticationResult = authResult
        };

        _cognitoMock.Setup(c => c.AdminInitiateAuthAsync(
            It.Is<AdminInitiateAuthRequest>(r =>
                r.AuthFlow == AuthFlowType.REFRESH_TOKEN_AUTH &&
                r.UserPoolId == tenantId &&
                r.AuthParameters["REFRESH_TOKEN"] == refreshTokenValue &&
                !string.IsNullOrEmpty(r.AuthParameters["SECRET_HASH"])),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);
        
        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.RefreshTokenAsync(tenantId, email, refreshTokenValue, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        var login = result.Value;
        login.ChallengeName.Should().Be(response.ChallengeName);
        login.Session.Should().Be(response.Session);
        login.TenantId.Should().Be(tenantId);
        login.AccessToken.Should().Be(authResult.AccessToken);
        login.RefreshToken.Should().Be(authResult.RefreshToken);
    }

    [Test]
    public async Task RefreshTokenAsync_Exception_ReturnsFailureAndLogsError()
    {
        // Arrange
        string tenantId = "tenantX";
        string email = "<EMAIL>";
        string refreshTokenValue = "asd";
        var exception = new Exception("error");
        _cognitoMock.Setup(c => c.AdminInitiateAuthAsync(It.IsAny<AdminInitiateAuthRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.RefreshTokenAsync(tenantId, email, refreshTokenValue, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(IdentityErrors.Cognito.REFRESH_GENERIC);
    }

    [Test]
    public async Task ForgotPasswordAsync_ClientCredentialsFailure_ReturnsGenericFailureAndLogsError()
    {
        // Arrange
        string tenantId = "tenantX";
        string email = "<EMAIL>";
        var credError = new Error("CRED_ERR", "no creds");

        _settingsServiceMock
            .Setup(s => s.GetConfiguration(It.IsAny<Application.Common.Models.Settings>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure<string>(credError));
        
        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.ForgotPasswordAsync(tenantId, email, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(IdentityErrors.Cognito.FORGOT_GENERIC);
    }

    [Test]
    public async Task ForgotPasswordAsync_Success_CallsForgotPasswordAndReturnsSuccess()
    {
        // Arrange
        string tenantId = "tenantX";
        string email = "<EMAIL>";

        _cognitoMock.Setup(c => c.ForgotPasswordAsync(
            It.Is<ForgotPasswordRequest>(r =>
                r.Username == email &&
                !string.IsNullOrEmpty(r.SecretHash)), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ForgotPasswordResponse());

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.ForgotPasswordAsync(tenantId, email, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Test]
    public async Task ForgotPasswordAsync_Exception_ReturnsGenericErrorAndLogsError()
    {
        // Arrange
        string tenantId = "tenantX";
        string email = "<EMAIL>";

        var exception = new Exception("oops");
        _cognitoMock.Setup(c => c.ForgotPasswordAsync(It.IsAny<ForgotPasswordRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);
        
        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.ForgotPasswordAsync(tenantId, email, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(IdentityErrors.Cognito.FORGOT_GENERIC);
    }

    [Test]
    public async Task ConfirmForgotPasswordAsync_ClientCredentialsFailure_ReturnsGenericFailureAndLogsError()
    {
        // Arrange
        string tenantId = "tenantX";
        string email = "<EMAIL>";
        string password = Guid.NewGuid().ToString();
        string code = "123456";
        var credError = new Error("CRED_ERR", "no creds");

        _settingsServiceMock
            .Setup(s => s.GetConfiguration(It.IsAny<Application.Common.Models.Settings>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure<string>(credError));
        
        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.ConfirmForgotPasswordAsync(tenantId, email, password, code, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(IdentityErrors.Cognito.CONFIRM_FORGOT_GENERIC);
    }
    
    [Test]
    public async Task ConfirmForgotPasswordAsync_Success_CallsConfirmForgotPasswordAndReturnsSuccess()
    {
        // Arrange
        string tenantId = "tenantX";
        string email = "<EMAIL>";
        string password = Guid.NewGuid().ToString();
        string code = "123456";

        _cognitoMock.Setup(c => c.ConfirmForgotPasswordAsync(
            It.Is<ConfirmForgotPasswordRequest>(r =>
                r.Username == email &&
                r.ConfirmationCode == code &&
                r.Password == password), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ConfirmForgotPasswordResponse());

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.ConfirmForgotPasswordAsync(tenantId, email, password, code, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }
    
    [TestCase(typeof(ExpiredCodeException), nameof(IdentityErrors.Cognito.CONFIRM_FORGOT_EXPIRED_CODE))]
    [TestCase(typeof(CodeMismatchException), nameof(IdentityErrors.Cognito.CONFIRM_FORGOT_CODE_MISMATCH))]
    [TestCase(typeof(InvalidPasswordException), nameof(IdentityErrors.Cognito.INVALID_PASS_FORMAT))]
    [TestCase(typeof(PasswordHistoryPolicyViolationException), nameof(IdentityErrors.Cognito.CONFIRM_FORGOT_PASSWORD_HISTORY))]
    public async Task ConfirmForgotPasswordAsync_ExceptionMapping_ReturnsExpectedErrorAndDoesNotLog(Type exceptionType, string errorConstantName)
    {
        // Arrange
        string tenantId = "tenantX";
        string email = "<EMAIL>";
        string password = Guid.NewGuid().ToString();
        string code = "123456";

        var exception = (Exception?)Activator.CreateInstance(exceptionType, "msg");
        _cognitoMock.Setup(c => c.ConfirmForgotPasswordAsync(It.IsAny<ConfirmForgotPasswordRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception!);

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.ConfirmForgotPasswordAsync(tenantId, email, password, code, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        var expectedError = (Error)(typeof(IdentityErrors.Cognito).GetField(errorConstantName))!.GetValue(null)!;
        result.Error.Should().Be(expectedError);

        _identityServiceLoggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Never);
    }

    [Test]
    public async Task ConfirmForgotPasswordAsync_GenericException_ReturnsGenericErrorAndLogsError()
    {
        // Arrange
        string tenantId = "tenantX";
        string email = "<EMAIL>";
        string password = Guid.NewGuid().ToString();
        string code = "123456";

        var exception = new Exception("oops");
        _cognitoMock.Setup(c => c.ConfirmForgotPasswordAsync(It.IsAny<ConfirmForgotPasswordRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        var identityService = new IdentityService(_cognitoMock.Object, _settingsServiceMock.Object, _tenantProviderMock.Object, _identityServiceLoggerMock.Object);

        // Act
        var result = await identityService.ConfirmForgotPasswordAsync(tenantId, email, password, code, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(IdentityErrors.Cognito.CONFIRM_FORGOT_GENERIC);
    }
}
