﻿using UsersManagement.Infrastructure.Identity;

namespace UsersManagement.Infrastructure.UnitTests.Identity;
public class CognitoChallengeValidatorTests
{
    [Test]
    public void ShouldReturnEmptyOnNotFound()
    {
        var validatedKeys = CognitoChallengeValidator.ValidateAndFilterKeys("test", []);

        validatedKeys.Should().BeEmpty();
    }

    [Test]
    public void ShouldReturnTruncateNonExistingKeys()
    {
        Dictionary<string, string> parameters = new()
        {
            { CognitoChallengeValidator.USERNAME, "test" },
            { "test", "test" }
        };
        var validatedKeys = CognitoChallengeValidator.ValidateAndFilterKeys("NEW_PASSWORD_REQUIRED", parameters);

        validatedKeys.Should().NotBeEmpty();
        validatedKeys.Count.Should().Be(parameters.Count - 1);
        validatedKeys[CognitoChallengeValidator.USERNAME].Should().Be("test");
    }
}
