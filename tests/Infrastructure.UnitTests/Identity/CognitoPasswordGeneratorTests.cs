﻿using UsersManagement.Infrastructure.Identity;

namespace UsersManagement.Infrastructure.UnitTests.Identity;
public class CognitoPasswordGeneratorTests
{
    private CognitoPasswordGenerator _passwordGenerator;
    private const string UppercaseChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private const string LowercaseChars = "abcdefghijklmnopqrstuvwxyz";
    private const string Numbers = "0123456789";
    private const string SpecialChars = "^$*.[]{}()?!\"@#%&/\\,><':;|_~`=+-";

    [SetUp]
    public void Setup()
    {
        _passwordGenerator = new CognitoPasswordGenerator();
    }

    [Test]
    public void Generate_ShouldReturnPasswordOfLength16()
    {
        // Act
        var password = _passwordGenerator.Generate();

        // Assert
        password.Should().NotBeNull();
        password.Length.Should().Be(16);
    }

    [Test]
    public void Generate_ShouldContainAtLeastOneUppercaseLetter()
    {
        // Act
        var password = _passwordGenerator.Generate();

        // Assert
        bool hasUppercase = password.Any(UppercaseChars.Contains);
        hasUppercase.Should().BeTrue("the password should contain at least one uppercase letter");
    }

    [Test]
    public void Generate_ShouldContainAtLeastOneLowercaseLetter()
    {
        // Act
        var password = _passwordGenerator.Generate();

        // Assert
        bool hasLowercase = password.Any(c => LowercaseChars.Contains(c));
        hasLowercase.Should().BeTrue("the password should contain at least one lowercase letter");
    }

    [Test]
    public void Generate_ShouldContainAtLeastOneDigit()
    {
        // Act
        var password = _passwordGenerator.Generate();

        // Assert
        bool hasDigit = password.Any(Numbers.Contains);
        hasDigit.Should().BeTrue("the password should contain at least one digit");
    }

    [Test]
    public void Generate_ShouldContainAtLeastOneSpecialCharacter()
    {
        // Act
        var password = _passwordGenerator.Generate();

        // Assert
        bool hasSpecial = password.Any(SpecialChars.Contains);
        hasSpecial.Should().BeTrue("the password should contain at least one special character");
    }

    [Test]
    public void Generate_MultipleInvocations_ShouldReturnDifferentPasswords()
    {
        // Act
        var password1 = _passwordGenerator.Generate();
        var password2 = _passwordGenerator.Generate();

        // Assert
        password1.Should().NotBe(password2, "each generated password should be random");
    }
}
