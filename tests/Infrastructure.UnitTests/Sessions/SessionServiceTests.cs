using Microsoft.Extensions.Logging;
using System.Text;
using UsersManagement.Infrastructure.DynamoDB.Abstractions;
using UsersManagement.Infrastructure.Sessions;

namespace UsersManagement.Infrastructure.UnitTests.Sessions
{
    [TestFixture]
    public class SessionServiceTests
    {
        private Mock<IDynamoDB> _dynamoDBMock;
        private Mock<ILogger<SessionService>> _loggerMock;
        private SessionService _sessionService;

        [SetUp]
        public void Setup()
        {
            _dynamoDBMock = new Mock<IDynamoDB>();
            _loggerMock = new Mock<ILogger<SessionService>>();
            _sessionService = new SessionService(_dynamoDBMock.Object, _loggerMock.Object);
        }

        #region StoreJwtAsync Tests

        [Test]
        public async Task StoreJwtAsync_WhenSuccessful_ReturnsBase64EncodedKey()
        {
            // Arrange
            string tenantId = "tenant123";
            string jwt = "jwt-token-123";
            var cancellationToken = CancellationToken.None;

            _dynamoDBMock
                .Setup(db => db.InsertItemAsync(
                    It.IsAny<string>(),
                    It.IsAny<Dictionary<string, object>>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Success());

            // Act
            var result = await _sessionService.StoreJwtAsync(tenantId, jwt, cancellationToken);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().NotBeNullOrEmpty();
            
            // Verify the base64 encoding
            byte[] decodedBytes = Convert.FromBase64String(result.Value);
            string decodedKey = Encoding.UTF8.GetString(decodedBytes);
            Guid.TryParse(decodedKey, out _).Should().BeTrue();

            // Verify DynamoDB was called with correct parameters
            _dynamoDBMock.Verify(db => db.InsertItemAsync(
                "TempStore",
                It.Is<Dictionary<string, object>>(dict => 
                    dict.ContainsKey("tenant_id") && dict["tenant_id"].Equals(tenantId) &&
                    dict.ContainsKey("jwt") && dict["jwt"].Equals(jwt) &&
                    dict.ContainsKey("id") && dict.ContainsKey("ttl")),
                cancellationToken), 
                Times.Once);
        }

        [Test]
        public async Task StoreJwtAsync_WhenDynamoDBFails_ReturnsFailure()
        {
            // Arrange
            string tenantId = "tenant123";
            string jwt = "jwt-token-123";
            var cancellationToken = CancellationToken.None;
            var error = new Error("DB_ERROR", "Database error");

            _dynamoDBMock
                .Setup(db => db.InsertItemAsync(
                    It.IsAny<string>(),
                    It.IsAny<Dictionary<string, object>>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Failure(error));

            // Act
            var result = await _sessionService.StoreJwtAsync(tenantId, jwt, cancellationToken);

            // Assert
            result.IsFailure.Should().BeTrue();
            result.Error.Should().Be(error);

            // Verify logger was called
            _loggerMock.Verify(x => x.Log(
                It.IsAny<LogLevel>(),
                It.IsAny<EventId>(),
                It.IsAny<It.IsAnyType>(),
                It.IsAny<Exception>(),
                (Func<It.IsAnyType, Exception?, string>)It.IsAny<object>()), Times.Once);
        }

        [Test]
        public async Task StoreJwtAsync_WhenExceptionOccurs_ReturnsFailure()
        {
            // Arrange
            string tenantId = "tenant123";
            string jwt = "jwt-token-123";
            var cancellationToken = CancellationToken.None;
            var exception = new Exception("Test exception");

            _dynamoDBMock
                .Setup(db => db.InsertItemAsync(
                    It.IsAny<string>(),
                    It.IsAny<Dictionary<string, object>>(),
                    It.IsAny<CancellationToken>()))
                .ThrowsAsync(exception);

            // Act
            var result = await _sessionService.StoreJwtAsync(tenantId, jwt, cancellationToken);

            // Assert
            result.IsFailure.Should().BeTrue();
            result.Error.Code.Should().Be("JWT_STORAGE_FAILED");

            // Verify logger was called
            _loggerMock.Verify(x => x.Log(
                It.IsAny<LogLevel>(),
                It.IsAny<EventId>(),
                It.IsAny<It.IsAnyType>(),
                exception,
                (Func<It.IsAnyType, Exception?, string>)It.IsAny<object>()), Times.Once);
        }

        #endregion

        #region GetJwtAsync Tests

        [Test]
        public async Task GetJwtAsync_WhenSuccessful_ReturnsJwt()
        {
            // Arrange
            string tenantId = "tenant123";
            string key = Guid.NewGuid().ToString();
            string base64Key = Convert.ToBase64String(Encoding.UTF8.GetBytes(key));
            string jwt = "jwt-token-123";
            var cancellationToken = CancellationToken.None;
            
            // Future timestamp (not expired)
            long futureTimestamp = DateTimeOffset.UtcNow.AddMinutes(5).ToUnixTimeSeconds();

            var items = new List<Dictionary<string, string>>
            {
                new Dictionary<string, string>
                {
                    { "jwt", jwt },
                    { "ttl", futureTimestamp.ToString() }
                }
            };

            _dynamoDBMock
                .Setup(db => db.GetMultipleItems(
                    It.IsAny<string>(),
                    It.IsAny<Dictionary<string, string>>(),
                    It.IsAny<Dictionary<string, object>>(),
                    It.IsAny<int>(),
                    It.IsAny<string?>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Success(items));

            // Act
            var result = await _sessionService.GetJwtAsync(tenantId, base64Key, cancellationToken);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().Be(jwt);
            
            // Verify DynamoDB was called with correct parameters
            _dynamoDBMock.Verify(db => db.GetMultipleItems(
                "TempStore",
                It.Is<Dictionary<string, string>>(dict => 
                    dict.ContainsKey("tenant_id") && dict["tenant_id"].Equals(tenantId) &&
                    dict.ContainsKey("id") && dict["id"].Equals(key)),
                It.IsAny<Dictionary<string, object>>(),
                1,
                It.IsAny<string?>(),
                cancellationToken), 
                Times.Once);
        }

        [Test]
        public async Task GetJwtAsync_WhenSessionExpired_ReturnsFailure()
        {
            // Arrange
            string tenantId = "tenant123";
            string key = Guid.NewGuid().ToString();
            string base64Key = Convert.ToBase64String(Encoding.UTF8.GetBytes(key));
            string jwt = "jwt-token-123";
            var cancellationToken = CancellationToken.None;
            
            // Past timestamp (expired)
            long pastTimestamp = DateTimeOffset.UtcNow.AddMinutes(-5).ToUnixTimeSeconds();

            var items = new List<Dictionary<string, string>>
            {
                new Dictionary<string, string>
                {
                    { "jwt", jwt },
                    { "ttl", pastTimestamp.ToString() }
                }
            };

            _dynamoDBMock
                .Setup(db => db.GetMultipleItems(
                    It.IsAny<string>(),
                    It.IsAny<Dictionary<string, string>>(),
                    It.IsAny<Dictionary<string, object>>(),
                    It.IsAny<int>(),
                    It.IsAny<string?>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Success(items));

            // Act
            var result = await _sessionService.GetJwtAsync(tenantId, base64Key, cancellationToken);

            // Assert
            result.IsFailure.Should().BeTrue();
            result.Error.Code.Should().Be("EXPIRED_SESSION");
        }

        [Test]
        public async Task GetJwtAsync_WhenDynamoDBFails_ReturnsFailure()
        {
            // Arrange
            string tenantId = "tenant123";
            string key = Guid.NewGuid().ToString();
            string base64Key = Convert.ToBase64String(Encoding.UTF8.GetBytes(key));
            var cancellationToken = CancellationToken.None;
            var error = new Error("DB_ERROR", "Database error");

            _dynamoDBMock
                .Setup(db => db.GetMultipleItems(
                    It.IsAny<string>(),
                    It.IsAny<Dictionary<string, string>>(),
                    It.IsAny<Dictionary<string, object>>(),
                    It.IsAny<int>(),
                    It.IsAny<string?>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Failure<List<Dictionary<string, string>>>(error));

            // Act
            var result = await _sessionService.GetJwtAsync(tenantId, base64Key, cancellationToken);

            // Assert
            result.IsFailure.Should().BeTrue();
            result.Error.Code.Should().Be("JWT_RETRIEVAL_FAILED");

            // Verify logger was called
            _loggerMock.Verify(x => x.Log(
                It.IsAny<LogLevel>(),
                It.IsAny<EventId>(),
                It.IsAny<It.IsAnyType>(),
                It.IsAny<Exception>(),
                (Func<It.IsAnyType, Exception?, string>)It.IsAny<object>()), Times.Once);
        }

        [Test]
        public async Task GetJwtAsync_WhenInvalidBase64Key_ReturnsFailure()
        {
            // Arrange
            string tenantId = "tenant123";
            string invalidBase64Key = "not-a-valid-base64-string";
            var cancellationToken = CancellationToken.None;

            // Act
            var result = await _sessionService.GetJwtAsync(tenantId, invalidBase64Key, cancellationToken);

            // Assert
            result.IsFailure.Should().BeTrue();
            result.Error.Code.Should().Be("JWT_RETRIEVAL_FAILED");

            // Verify logger was called with exception
            _loggerMock.Verify(x => x.Log(
                It.IsAny<LogLevel>(),
                It.IsAny<EventId>(),
                It.IsAny<It.IsAnyType>(),
                It.IsAny<Exception>(),
                (Func<It.IsAnyType, Exception?, string>)It.IsAny<object>()), Times.Once);
        }

        [Test]
        public async Task GetJwtAsync_WhenNoItemsReturned_ReturnsFailure()
        {
            // Arrange
            string tenantId = "tenant123";
            string key = Guid.NewGuid().ToString();
            string base64Key = Convert.ToBase64String(Encoding.UTF8.GetBytes(key));
            var cancellationToken = CancellationToken.None;

            // Empty list
            var items = new List<Dictionary<string, string>>();

            _dynamoDBMock
                .Setup(db => db.GetMultipleItems(
                    It.IsAny<string>(),
                    It.IsAny<Dictionary<string, string>>(),
                    It.IsAny<Dictionary<string, object>>(),
                    It.IsAny<int>(),
                    It.IsAny<string?>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Success(items));

            // Act
            var result = await _sessionService.GetJwtAsync(tenantId, base64Key, cancellationToken);

            // Assert
            result.IsFailure.Should().BeTrue();
            result.Error.Code.Should().Be("JWT_RETRIEVAL_FAILED");
        }
    }
    #endregion
}
