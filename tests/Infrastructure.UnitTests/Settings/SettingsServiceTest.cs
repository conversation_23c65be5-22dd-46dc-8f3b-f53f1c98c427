﻿using System.Threading;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Refit;
using UsersManagement.Application.Common.Interfaces;
using UsersManagement.Infrastructure.Settings;

namespace UsersManagement.Infrastructure.UnitTests.Settings;

public class SettingsServiceTest
{
    private Mock<ISettingsApi> _settingsApiMock;
    private Mock<ITenantProvider> _tenantProviderMock;
    private Mock<ILogger<SettingsService>> _settingsLoggerMock;
    private SettingsService _settingsService;
    private IConfiguration _configuration;
    private MemoryCache _memoryCache;
    private const string CognitoClientIdValue = "ClientId";

    [SetUp]
    public void Setup()
    {
        _settingsApiMock = new Mock<ISettingsApi>();

        _settingsApiMock
            .Setup(x => x.GetConfig(It.IsAny<IDictionary<string, string>>(), Application.Common.Models.Settings.CognitoClientId.ToString(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new KeyValuePair<string, string>(Application.Common.Models.Settings.CognitoClientId.ToString(), CognitoClientIdValue));

        _tenantProviderMock = new Mock<ITenantProvider>();
        _tenantProviderMock
            .Setup(x => x.GetTenantId())
            .Returns("test");

        _settingsLoggerMock = new Mock<ILogger<SettingsService>>();
        
        _memoryCache = new MemoryCache(new MemoryCacheOptions());

        _configuration = new ConfigurationBuilder()
            .Build();

        _settingsService = new SettingsService(_configuration, _settingsApiMock.Object, _tenantProviderMock.Object, _memoryCache, _settingsLoggerMock.Object);
    }

    [TearDown]
    public void TearDown()
    {
        _memoryCache.Dispose();
    }

    [Test]
    public async Task ShouldReturnClientIdValueAndSaveIntoMemory()
    {
        var tenantProvider = _tenantProviderMock.Object;
        var settingsService = new SettingsService(_configuration, _settingsApiMock.Object, tenantProvider, _memoryCache, _settingsLoggerMock.Object);

        var result = await settingsService.GetConfiguration(Application.Common.Models.Settings.CognitoClientId);

        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(CognitoClientIdValue);

        _settingsApiMock.Verify(x =>
            x.GetConfig(It.IsAny<IDictionary<string, string>>(), Application.Common.Models.Settings.CognitoClientId.ToString(), It.IsAny<CancellationToken>()),
            Times.Once());

        _memoryCache.Count.Should().Be(1);
        _memoryCache.Get($"{Application.Common.Models.Settings.CognitoClientId}-{tenantProvider.GetTenantId()}").Should().NotBeNull();
    }

    [Test]
    public async Task ShouldReturnClientIdValueShouldNotCallApi()
    {
        var tenantProvider = _tenantProviderMock.Object;

        _memoryCache.Set($"{Application.Common.Models.Settings.CognitoClientId}-{tenantProvider.GetTenantId()}", CognitoClientIdValue);

        var settingsService = new SettingsService(_configuration, new Mock<ISettingsApi>().Object, tenantProvider, _memoryCache, _settingsLoggerMock.Object);

        var result = await settingsService.GetConfiguration(Application.Common.Models.Settings.CognitoClientId);

        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(CognitoClientIdValue);

        _settingsApiMock.Verify(x =>
            x.GetConfig(It.IsAny<IDictionary<string, string>>(), Application.Common.Models.Settings.CognitoClientId.ToString(), It.IsAny<CancellationToken>()),
            Times.Never());
    }

    [Test]
    public async Task ShouldReturnClientIdValueShouldCallApiOnlyFirstTime()
    {
        var tenantProvider = _tenantProviderMock.Object;

        var settingsService = new SettingsService(_configuration, _settingsApiMock.Object, tenantProvider, _memoryCache, _settingsLoggerMock.Object);

        var result1 = await settingsService.GetConfiguration(Application.Common.Models.Settings.CognitoClientId);
        var result2 = await settingsService.GetConfiguration(Application.Common.Models.Settings.CognitoClientId);

        result1.Should().NotBeNull();
        result1.IsSuccess.Should().BeTrue();
        result1.Value.Should().Be(CognitoClientIdValue);

        result2.Should().BeEquivalentTo(result1);

        _settingsApiMock.Verify(x =>
            x.GetConfig(It.IsAny<IDictionary<string, string>>(), Application.Common.Models.Settings.CognitoClientId.ToString(), It.IsAny<CancellationToken>()),
            Times.Once());
    }

    [Test]
    public async Task ShouldReturnClientIdValueFromConfigurationAndSaveIntoMemory()
    {
        var tenantProvider = _tenantProviderMock.Object;
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                { $"{Application.Common.Models.Settings.CognitoClientId}", CognitoClientIdValue }
            })
            .Build();

        var settingsService = new SettingsService(configuration, new Mock<ISettingsApi>().Object, tenantProvider, _memoryCache, _settingsLoggerMock.Object);

        var result = await settingsService.GetConfiguration(Application.Common.Models.Settings.CognitoClientId);

        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(CognitoClientIdValue);

        _memoryCache.Count.Should().Be(1);
        _memoryCache.Get($"{Application.Common.Models.Settings.CognitoClientId}-{tenantProvider.GetTenantId()}").Should().NotBeNull();
        
        _settingsApiMock.Verify(x =>
            x.GetConfig(It.IsAny<IDictionary<string, string>>(), Application.Common.Models.Settings.CognitoClientId.ToString(), It.IsAny<CancellationToken>()),
            Times.Never());
    }

    [Test]
    public async Task ShouldReturnFailureNotFound()
    {
        var tenantProvider = _tenantProviderMock.Object;

        var settingsService = new SettingsService(_configuration, new Mock<ISettingsApi>().Object, tenantProvider, _memoryCache, _settingsLoggerMock.Object);

        var result = await settingsService.GetConfiguration(Application.Common.Models.Settings.CognitoClientId);

        result.Should().NotBeNull();
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().NotBeNullOrWhiteSpace();
        result.Error.Message.Should().NotBeNullOrWhiteSpace();

        _memoryCache.Count.Should().Be(1);
        _memoryCache.Get($"{Application.Common.Models.Settings.CognitoClientId}-{tenantProvider.GetTenantId()}").Should().BeNull();
    }

    [Test]
    public async Task GetConfiguration_ReturnsFailure_WhenExceptionThrown()
    {
        // Arange
        string tenantId = "tenant1";
        _settingsApiMock
            .Setup(x => x.GetConfig(It.IsAny<IDictionary<string, string>>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("API error"));

        // Act
        var result = await _settingsService.GetConfiguration(Application.Common.Models.Settings.CognitoClientId, tenantId, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("SettingsService.GetConfiguration");
        result.Error.Message.Should().Be("Error while attempting to obtain setting");

        _memoryCache.Count.Should().Be(0);
        _memoryCache.Get($"{Application.Common.Models.Settings.CognitoClientId}-{tenantId}").Should().BeNull();

        _settingsLoggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Test]
    public async Task GetJWKAsync_ReturnsSuccess_WhenConfigIsNotNull()
    {
        // Arrange
        string tenantId = "tenant1";
        string expectedJWK = "jwk-string";
        // Se configura el settingsApi para retornar un valor válido.
        _settingsApiMock
            .Setup(x => x.GetJWK(
                It.Is<IDictionary<string, string>>(d => d.ContainsKey("X-Tenant-Id") && d["X-Tenant-Id"] == tenantId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedJWK);

        var settingsService = new SettingsService(_configuration, _settingsApiMock.Object, _tenantProviderMock.Object, _memoryCache, _settingsLoggerMock.Object);

        // Act
        var result = await settingsService.GetJWKAsync(tenantId, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(expectedJWK);
    }

    [Test]
    public async Task GetJWKAsync_ReturnsFailure_WhenConfigIsNull()
    {
        // Arrange
        string tenantId = "tenant1";
        
        string? configValue = null;

        _settingsApiMock
            .Setup(x => x.GetJWK(It.IsAny<IDictionary<string, string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(configValue);

        var settingsService = new SettingsService(_configuration, _settingsApiMock.Object, _tenantProviderMock.Object, _memoryCache, _settingsLoggerMock.Object);

        // Act
        var result = await settingsService.GetJWKAsync(tenantId, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("SettingsService.GetJWKAsync");
        result.Error.Message.Should().Be("Error while attempting to obtain JWK");
    }

    [Test]
    public async Task GetJWKAsync_ReturnsFailure_WhenExceptionThrown()
    {
        // Arrange
        string tenantId = "tenant1";
        _settingsApiMock
            .Setup(x => x.GetJWK(It.IsAny<IDictionary<string, string>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("API error"));

        // Act
        var result = await _settingsService.GetJWKAsync(tenantId, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("SettingsService.GetJWKAsync");
        result.Error.Message.Should().Be("Error while attempting to obtain JWK");

        _settingsLoggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

}
