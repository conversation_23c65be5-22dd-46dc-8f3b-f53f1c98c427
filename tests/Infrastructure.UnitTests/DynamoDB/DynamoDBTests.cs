﻿using System.Net;
using System.Text;
using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DocumentModel;
using Amazon.DynamoDBv2.Model;
using FluentAssertions.Equivalency;
using Microsoft.Extensions.Logging;

namespace UsersManagement.Infrastructure.UnitTests.DynamoDB;

public class DynamoDBTests
{
    private Mock<IAmazonDynamoDB> _dynamoDbMock;
    private Mock<ILogger<Infrastructure.DynamoDB.DynamoDB>> _loggerMock;
    private Infrastructure.DynamoDB.DynamoDB _dynamoDb;

    [SetUp]
    public void SetUp()
    {
        _dynamoDbMock = new Mock<IAmazonDynamoDB>();
        _loggerMock = new Mock<ILogger<Infrastructure.DynamoDB.DynamoDB>>();
        _dynamoDb = new Infrastructure.DynamoDB.DynamoDB(_dynamoDbMock.Object, _loggerMock.Object);
    }

    [Test]
    public async Task GetValueAsync_ReturnsSuccess_WhenItemExists()
    {
        // Arrange
        var table = "TestTable";
        var keyField = "Id";
        var field = "Name";
        var key = "123";
        var expectedValue = "TestName";

        var response = new GetItemResponse
        {
            HttpStatusCode = HttpStatusCode.OK,
            Item = new Dictionary<string, AttributeValue>
            {
                { field, new AttributeValue { S = expectedValue } }
            }
        };

        _dynamoDbMock
            .Setup(db => db.GetItemAsync(It.IsAny<GetItemRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _dynamoDb.GetValueAsync(table, keyField, field, key);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(expectedValue);
    }

    [Test]
    public async Task GetValueAsync_ReturnsFailure_WhenItemDoesNotExist()
    {
        // Arrange
        var table = "TestTable";
        var keyField = "Id";
        var field = "Name";
        var key = "123";

        var response = new GetItemResponse
        {
            HttpStatusCode = HttpStatusCode.OK,
            Item = new Dictionary<string, AttributeValue>()
        };

        _dynamoDbMock
            .Setup(db => db.GetItemAsync(It.IsAny<GetItemRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _dynamoDb.GetValueAsync(table, keyField, field, key);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().NotBeNull();
    }

    [Test]
    public async Task GetValueAsync_LogsError_WhenExceptionThrown()
    {
        // Arrange
        var table = "TestTable";
        var keyField = "Id";
        var field = "Name";
        var key = "123";

        _dynamoDbMock
            .Setup(db => db.GetItemAsync(It.IsAny<GetItemRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("DynamoDB error"));

        // Act
        var result = await _dynamoDb.GetValueAsync(table, keyField, field, key);

        // Assert
        result.IsSuccess.Should().BeFalse();
        _loggerMock.Verify(
            x => x.Log(
                It.Is<LogLevel>(x => x == LogLevel.Error),
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => (v.ToString() ?? "").Contains("DynamoDB: Error while trying to retrieve value")),
                It.IsAny<Exception>(),
                (Func<It.IsAnyType, Exception?, string>)It.IsAny<object>()), Times.Once);
    }

    [Test]
    public async Task GetValueAsync_ReturnsFailure_WhenHttpStatusCodeIsNotOk()
    {
        // Arrange
        var table = "TestTable";
        var keyField = "Id";
        var field = "Name";
        var key = "123";

        var response = new GetItemResponse
        {
            HttpStatusCode = HttpStatusCode.InternalServerError,
            Item = new Dictionary<string, AttributeValue>()
        };

        _dynamoDbMock
            .Setup(db => db.GetItemAsync(It.IsAny<GetItemRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _dynamoDb.GetValueAsync(table, keyField, field, key);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Error.Message.Should().Be("Error while trying to retrieve value");
    }

    [Test]
    public async Task GetValuesAsync_ReturnsSuccess_WhenItemExists()
    {
        // Arrange
        var table = "TestTable";
        var keyField = "Id";
        var key = "123";
        var expectedValues = new Dictionary<string, string>
        {
            { "Name", "TestName" },
            { "Age", "30" }
        };

        var response = new GetItemResponse
        {
            HttpStatusCode = HttpStatusCode.OK,
            Item = new Dictionary<string, AttributeValue>
            {
                { "Name", new AttributeValue { S = "TestName" } },
                { "Age", new AttributeValue { S = "30" } }
            }
        };

        _dynamoDbMock
            .Setup(db => db.GetItemAsync(It.IsAny<GetItemRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _dynamoDb.GetValuesAsync(table, keyField, key);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeEquivalentTo(expectedValues);
    }

    [Test]
    public async Task GetValuesAsync_ReturnsFailure_WhenItemDoesNotExist()
    {
        // Arrange
        var table = "TestTable";
        var keyField = "Id";
        var key = "123";

        _dynamoDbMock
            .Setup(db => db.GetItemAsync(It.IsAny<GetItemRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new KeyNotFoundException());

        // Act
        var result = await _dynamoDb.GetValuesAsync(table, keyField, key);

        // Assert
        result.IsFailure.Should().BeTrue();
    }

    [Test]
    public async Task GetValuesAsync_LogsError_WhenExceptionThrown()
    {
        // Arrange
        var table = "TestTable";
        var keyField = "Id";
        var key = "123";

        _dynamoDbMock
            .Setup(db => db.GetItemAsync(It.IsAny<GetItemRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("DynamoDB error"));

        // Act
        var result = await _dynamoDb.GetValuesAsync(table, keyField, key);

        // Assert

        result.IsSuccess.Should().BeFalse();
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => (v.ToString() ?? "").Contains("DynamoDB: Error while trying to retrieve value")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Test]
    public async Task GetValuesAsync_ReturnsFailure_WhenHttpStatusCodeIsNotOk()
    {
        // Arrange
        var table = "TestTable";
        var keyField = "Id";
        var key = "123";

        var response = new GetItemResponse
        {
            HttpStatusCode = HttpStatusCode.InternalServerError,
            Item = new Dictionary<string, AttributeValue>()
        };

        _dynamoDbMock
            .Setup(db => db.GetItemAsync(It.IsAny<GetItemRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _dynamoDb.GetValuesAsync(table, keyField, key);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Error.Message.Should().Be("Error while trying to retrieve value");
    }

    [Test]
    public async Task UpdateValueAsync_ValidInputsAndDynamoDBReturnsOK_ReturnsSuccess()
    {
        // Arrange
        var table = "ai-licenses";
        var keyField = "tenant_id";
        var field = "used";
        var key = "some-tenant-id";
        var newValue = "10";

        _dynamoDbMock
            .Setup(x => x.UpdateItemAsync(It.IsAny<UpdateItemRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new UpdateItemResponse { HttpStatusCode = HttpStatusCode.OK });

        // Act
        var result = await _dynamoDb.UpdateValueAsync(table, keyField, field, key, newValue, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Test]
    public async Task UpdateValueAsync_DynamoDBReturnsNonOKStatus_ReturnsFailure()
    {
        // Arrange
        var table = "ai-licenses";
        var keyField = "tenant_id";
        var field = "used";
        var key = "some-tenant-id";
        var newValue = "10";
        var cancellationToken = CancellationToken.None;

        _dynamoDbMock
            .Setup(x => x.UpdateItemAsync(It.IsAny<UpdateItemRequest>(), cancellationToken))
            .ReturnsAsync(new UpdateItemResponse { HttpStatusCode = HttpStatusCode.InternalServerError });

        // Act
        var result = await _dynamoDb.UpdateValueAsync(table, keyField, field, key, newValue, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().NotBeNullOrWhiteSpace();
        result.Error.Message.Should().NotBeNullOrWhiteSpace();
    }

    [Test]
    public async Task UpdateValueAsync_ThrowsException_LogsErrorAndReturnsFailure()
    {
        // Arrange
        var table = "ai-licenses";
        var keyField = "tenant_id";
        var field = "used";
        var key = "some-tenant-id";
        var newValue = "10";
        var cancellationToken = CancellationToken.None;

        var exception = new Exception("DynamoDB service exception");

        _dynamoDbMock
            .Setup(x => x.UpdateItemAsync(It.IsAny<UpdateItemRequest>(), cancellationToken))
            .ThrowsAsync(exception);

        // Act
        var result = await _dynamoDb.UpdateValueAsync(table, keyField, field, key, newValue, cancellationToken);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().NotBeNullOrWhiteSpace();
        result.Error.Message.Should().NotBeNullOrWhiteSpace();


        _loggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Test]
    public async Task GetMultipleItems_OverloadWithKeyField_CallsQueryAndMapsValues()
    {
        // Arrange
        string table = "MyTable";
        string keyField = "pk";
        string keyValue = "key1";
        var response = new QueryResponse
        {
            HttpStatusCode = System.Net.HttpStatusCode.OK,
            Items = new List<Dictionary<string, AttributeValue>>
                {
                    new Dictionary<string, AttributeValue>
                    {
                        { "s", new AttributeValue { S = "abc" } },
                        { "n", new AttributeValue { N = "123" } },
                        { "b", new AttributeValue { BOOL = true } },
                        { "empty", new AttributeValue() }
                    }
                }
        };
        _dynamoDbMock
            .Setup(db => db.QueryAsync(It.IsAny<QueryRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _dynamoDb.GetMultipleItems(table, keyField, keyValue);

        // Assert
        result.IsSuccess.Should().BeTrue();
        var list = result.Value;
        list.Should().HaveCount(1);
        var dict = list[0];
        dict["s"].Should().Be("abc");
        dict["n"].Should().Be("123");
        dict["b"].Should().Be("True");
        dict["empty"].Should().BeEmpty();
    }

    [Test]
    public async Task GetMultipleItems_WithFilters_CallsQueryWithFilterExpression()
    {
        // Arrange
        string table = "MyTable";
        string keyField = "pk";
        string keyValue = "key1";
        var keyFilter = new Dictionary<string, string> { { keyField, keyValue } };
        var filters = new Dictionary<string, object> { { "value", new List<string> { "v1", "v2" } }, { "test", "abc" } };
        var response = new QueryResponse { Items = new List<Dictionary<string, AttributeValue>>() };
        _dynamoDbMock
            .Setup(db => db.QueryAsync(It.IsAny<QueryRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _dynamoDb.GetMultipleItems(table, keyFilter, filters, pageSize: 10);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeEmpty();
    }

    [Test]
    public async Task GetMultipleItems_QueryThrows_ReturnsFailure()
    {
        // Arrange
        string table = "MyTable";
        string keyField = "pk";
        string keyValue = "key1";
        var exception = new Exception("query error");
        _dynamoDbMock.Setup(db => db.QueryAsync(It.IsAny<QueryRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        // Act
        var result = await _dynamoDb.GetMultipleItems(table, keyField, keyValue);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("DynamoDB.GetMultipleItems");
    }

    [Test]
    public async Task GetMultipleItems_WithNextToken_PassesExclusiveStartKeyDecoded()
    {
        // Arrange
        string table = "MyTable";
        string keyField = "pk";
        string keyValue = "key1";
        var decodedMap = new Dictionary<string, AttributeValue>
            {
                { keyField, new AttributeValue { S = keyValue } }
            };
        var tokenJson = "{\"" + keyField + "\":{\"S\":\"" + keyValue + "\"}}";
        var nextToken = Convert.ToBase64String(Encoding.UTF8.GetBytes(tokenJson));
        var response = new QueryResponse { Items = new List<Dictionary<string, AttributeValue>>() };
        _dynamoDbMock
            .Setup(db => db.QueryAsync(It.IsAny<QueryRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _dynamoDb.GetMultipleItems(table, keyField, keyValue, pageSize: 10, nextToken: nextToken);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Test]
    public async Task AddBulkAsync_Success_ReturnsSuccess()
    {
        // Arrange
        string table = "MyTable";
        string keyField = "pk";
        string keyValue = "key1";
        string field = "count";
        var keyFilter = new Dictionary<string, string> { { keyField, keyValue } };
        var filters = new Dictionary<string, object>();
        _dynamoDbMock.Setup(db => db.UpdateItemAsync(
            It.IsAny<UpdateItemRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new UpdateItemResponse { HttpStatusCode = HttpStatusCode.OK });

        // Act
        var result = await _dynamoDb.AddBulkAsync(table, keyFilter, filters, field, addQty: 5);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Test]
    public async Task AddBulkAsync_UpdateThrows_ReturnsFailureWithError()
    {
        // Arrange
        string table = "MyTable";
        string keyField = "pk";
        string keyValue = "key1";
        string field = "count";
        var keyFilter = new Dictionary<string, string> { { keyField, keyValue } };
        var filters = new Dictionary<string, object>();
        var exception = new Exception("update error");
        _dynamoDbMock.Setup(db => db.UpdateItemAsync(It.IsAny<UpdateItemRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        // Act
        var result = await _dynamoDb.AddBulkAsync(table, keyFilter, filters, field, addQty: 2);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("DynamoDB.UpdateBulkAsync");
    }
    
    #region InsertItemAsync Tests
    
    [Test]
    public async Task InsertItemAsync_WithStringValues_ReturnsSuccess()
    {
        // Arrange
        string table = "TestTable";
        var item = new Dictionary<string, object>
        {
            { "id", "123" },
            { "name", "Test Item" },
            { "description", "This is a test item" }
        };
        var cancellationToken = CancellationToken.None;
        
        _dynamoDbMock
            .Setup(db => db.PutItemAsync(It.IsAny<PutItemRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new PutItemResponse { HttpStatusCode = HttpStatusCode.OK });
        
        // Act
        var result = await _dynamoDb.InsertItemAsync(table, item, cancellationToken);
        
        // Assert
        result.IsSuccess.Should().BeTrue();
        
        // Verify PutItemAsync was called with correct parameters
        _dynamoDbMock.Verify(db => db.PutItemAsync(
            It.Is<PutItemRequest>(req => 
                req.TableName == table && 
                req.Item.Count == 3 &&
                req.Item["id"].S == "123" &&
                req.Item["name"].S == "Test Item" &&
                req.Item["description"].S == "This is a test item"),
            cancellationToken), 
            Times.Once);
    }
    
    [Test]
    public async Task InsertItemAsync_WithNumericValues_ReturnsSuccess()
    {
        // Arrange
        string table = "TestTable";
        var item = new Dictionary<string, object>
        {
            { "id", "123" },
            { "count", 42 },
            { "amount", 99L }
        };
        var cancellationToken = CancellationToken.None;
        
        _dynamoDbMock
            .Setup(db => db.PutItemAsync(It.IsAny<PutItemRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new PutItemResponse { HttpStatusCode = HttpStatusCode.OK });
        
        // Act
        var result = await _dynamoDb.InsertItemAsync(table, item, cancellationToken);
        
        // Assert
        result.IsSuccess.Should().BeTrue();
        
        // Verify PutItemAsync was called with correct parameters
        _dynamoDbMock.Verify(db => db.PutItemAsync(
            It.Is<PutItemRequest>(req => 
                req.TableName == table && 
                req.Item.Count == 3 &&
                req.Item["id"].S == "123" &&
                req.Item["count"].N == "42" &&
                req.Item["amount"].N == "99"),
            cancellationToken), 
            Times.Once);
    }
    
    [Test]
    public async Task InsertItemAsync_WithBooleanValues_ReturnsSuccess()
    {
        // Arrange
        string table = "TestTable";
        var item = new Dictionary<string, object>
        {
            { "id", "123" },
            { "isActive", true },
            { "isDeleted", false }
        };
        var cancellationToken = CancellationToken.None;
        
        _dynamoDbMock
            .Setup(db => db.PutItemAsync(It.IsAny<PutItemRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new PutItemResponse { HttpStatusCode = HttpStatusCode.OK });
        
        // Act
        var result = await _dynamoDb.InsertItemAsync(table, item, cancellationToken);
        
        // Assert
        result.IsSuccess.Should().BeTrue();
        
        // Verify PutItemAsync was called with correct parameters
        _dynamoDbMock.Verify(db => db.PutItemAsync(
            It.Is<PutItemRequest>(req => 
                req.TableName == table && 
                req.Item.Count == 3 &&
                req.Item["id"].S == "123" &&
                req.Item["isActive"].BOOL == true &&
                req.Item["isDeleted"].BOOL == false),
            cancellationToken), 
            Times.Once);
    }
    
    [Test]
    public async Task InsertItemAsync_WithMixedValues_ReturnsSuccess()
    {
        // Arrange
        string table = "TestTable";
        var item = new Dictionary<string, object>
        {
            { "id", "123" },
            { "count", 42 },
            { "isActive", true },
            { "customObject", new { Name = "Test", Value = 123 } } // Custom object that will use ToString()
        };
        var cancellationToken = CancellationToken.None;
        
        _dynamoDbMock
            .Setup(db => db.PutItemAsync(It.IsAny<PutItemRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new PutItemResponse { HttpStatusCode = HttpStatusCode.OK });
        
        // Act
        var result = await _dynamoDb.InsertItemAsync(table, item, cancellationToken);
        
        // Assert
        result.IsSuccess.Should().BeTrue();
        
        // Verify PutItemAsync was called with correct parameters and custom object is converted to string
        _dynamoDbMock.Verify(db => db.PutItemAsync(
            It.Is<PutItemRequest>(req => 
                req.TableName == table && 
                req.Item.Count == 4 &&
                req.Item["id"].S == "123" &&
                req.Item["count"].N == "42" &&
                req.Item["isActive"].BOOL == true &&
                req.Item["customObject"].S != null),
            cancellationToken), 
            Times.Once);
    }
    
    [Test]
    public async Task InsertItemAsync_WithNullValue_ReturnsSuccess()
    {
        // Arrange
        string table = "TestTable";
        var item = new Dictionary<string, object>
        {
            { "id", "123" },
            { "nullValue", null! }
        };
        var cancellationToken = CancellationToken.None;
        
        _dynamoDbMock
            .Setup(db => db.PutItemAsync(It.IsAny<PutItemRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new PutItemResponse { HttpStatusCode = HttpStatusCode.OK });
        
        // Act
        var result = await _dynamoDb.InsertItemAsync(table, item, cancellationToken);
        
        // Assert
        result.IsSuccess.Should().BeTrue();
        
        // Verify PutItemAsync was called with correct parameters and null is handled
        _dynamoDbMock.Verify(db => db.PutItemAsync(
            It.Is<PutItemRequest>(req => 
                req.TableName == table && 
                req.Item.Count == 2 &&
                req.Item["id"].S == "123" &&
                req.Item["nullValue"].S == string.Empty),
            cancellationToken), 
            Times.Once);
    }
    
    [Test]
    public async Task InsertItemAsync_WhenHttpStatusCodeIsNotOK_ReturnsFailure()
    {
        // Arrange
        string table = "TestTable";
        var item = new Dictionary<string, object>
        {
            { "id", "123" },
            { "name", "Test Item" }
        };
        var cancellationToken = CancellationToken.None;
        
        _dynamoDbMock
            .Setup(db => db.PutItemAsync(It.IsAny<PutItemRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new PutItemResponse { HttpStatusCode = HttpStatusCode.InternalServerError });
        
        // Act
        var result = await _dynamoDb.InsertItemAsync(table, item, cancellationToken);
        
        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("DynamoDB.InsertItemAsync");
        result.Error.Message.Should().Be("Error inserting item into DynamoDB");

        // Verify logger was called
        _loggerMock.Verify(x => x.Log(
            It.IsAny<LogLevel>(),
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            (Func<It.IsAnyType, Exception?, string>)It.IsAny<object>()), Times.Once);
    }
    
    [Test]
    public async Task InsertItemAsync_WhenExceptionThrown_ReturnsFailure()
    {
        // Arrange
        string table = "TestTable";
        var item = new Dictionary<string, object>
        {
            { "id", "123" },
            { "name", "Test Item" }
        };
        var cancellationToken = CancellationToken.None;
        var exception = new Exception("DynamoDB error");
        
        _dynamoDbMock
            .Setup(db => db.PutItemAsync(It.IsAny<PutItemRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);
        
        // Act
        var result = await _dynamoDb.InsertItemAsync(table, item, cancellationToken);
        
        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("DynamoDB.InsertItemAsync");
        result.Error.Message.Should().Be("Error inserting item into DynamoDB");

        // Verify logger was called with exception
        _loggerMock.Verify(x => x.Log(
            It.IsAny<LogLevel>(),
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            (Func<It.IsAnyType, Exception?, string>)It.IsAny<object>()), Times.Once);
    }
    
    #endregion
}
