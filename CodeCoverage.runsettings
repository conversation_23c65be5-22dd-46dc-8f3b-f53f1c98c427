﻿<?xml version="1.0" encoding="utf-8"?>
<RunSettings>
  <DataCollectionRunSettings>
    <DataCollectors>
      <DataCollector friendlyName="Code Coverage">
        <Configuration>
          <CodeCoverage>
            <ModulePaths>
              <Exclude>
                <!-- Excluir ensamblados específicos si es necesario -->
                <ModulePath>refit.dll$</ModulePath>
                <ModulePath>refit.*.dll$</ModulePath>
                <ModulePath>fluentvalidation.dll$</ModulePath>
                <ModulePath>fluentvalidation.*.dll$</ModulePath>
                <ModulePath>fluentvalidation.*.dll$</ModulePath>
                <ModulePath>.*Tests.dll$</ModulePath>
              </Exclude>
            </ModulePaths>
            <Functions>
              <Exclude>
                <!-- Excluir funciones dentro del espacio de nombres especificado -->
                <Function>^Microsoft.Extensions.DependencyInjection</Function>
                <Function>^UsersManagement.API.Infrastructure.Filters</Function>
                <Function>^UsersManagement.API.Endpoints.V1</Function>
                <Function>^UsersManagement.API.UnitTests</Function>
              </Exclude>
            </Functions>
            <Sources>
              <Exclude>
                .*Program\.cs$
              </Exclude>
            </Sources>
          </CodeCoverage>
        </Configuration>
      </DataCollector>
    </DataCollectors>
  </DataCollectionRunSettings>
</RunSettings>