﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <packageSources>
    <!--To inherit the global NuGet package sources remove the <clear/> line below -->
    <clear />
    <add key="nuget" value="https://api.nuget.org/v3/index.json" />
    <add key="Finsky" value="https://pkgs.dev.azure.com/n5/Finsky/_packaging/Finsky/nuget/v3/index.json" />
    <add key="Platform" value="https://pkgs.dev.azure.com/n5/Platform/_packaging/Platform/nuget/v3/index.json" />
  </packageSources>
  <packageSourceMapping>
    <packageSource key="nuget">
      <package pattern="*" />
    </packageSource>
    <packageSource key="Finsky">
      <package pattern="N5.*" />
    </packageSource>
    <packageSource key="Platform">
      <package pattern="N5.Platform.*" />
    </packageSource>
    <packageSource key="feed-Finsky">
      <package pattern="N5.*" />
    </packageSource>
    <packageSource key="feed-Platform">
      <package pattern="N5.Platform.*" />
    </packageSource>
  </packageSourceMapping>
</configuration>
