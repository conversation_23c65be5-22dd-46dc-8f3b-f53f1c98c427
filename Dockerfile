FROM mcr.microsoft.com/dotnet/sdk:8.0-noble AS build
ARG FEED_ACCESSTOKEN
ENV VSS_NUGET_EXTERNAL_FEED_ENDPOINTS="{\"endpointCredentials\": [{\"endpoint\":\"https://pkgs.dev.azure.com/n5/Finsky/_packaging/Finsky/nuget/v3/index.json\", \"username\":\"docker\", \"password\":\"${FEED_ACCESSTOKEN}\"}, {\"endpoint\":\"https://pkgs.dev.azure.com/n5/Platform/_packaging/Platform/nuget/v3/index.json\", \"username\":\"docker\", \"password\":\"${FEED_ACCESSTOKEN}\"}]}"

RUN curl -L https://raw.githubusercontent.com/Microsoft/artifacts-credprovider/master/helpers/installcredprovider.sh  | sh

WORKDIR /app
COPY . .

RUN dotnet restore "src/UsersManagement.API/UsersManagement.API.csproj" && \
    dotnet publish "src/UsersManagement.API/UsersManagement.API.csproj" \
      -c Release \
      -o ./out \
      -r linux-x64

FROM mcr.microsoft.com/dotnet/aspnet:8.0-noble-chiseled-extra AS runtime
ARG APP_DIR="/app" \
    ENV

ENV ASPNETCORE_URLS="http://*:8080" \
    ASPNETCORE_ENVIRONMENT="${ENV:-Development}"

WORKDIR $APP_DIR

COPY --from=build --chown=app:app /app/out $APP_DIR
ENTRYPOINT ["dotnet", "UsersManagement.API.dll"]
